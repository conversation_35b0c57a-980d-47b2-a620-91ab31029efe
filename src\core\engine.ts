import { promises as fs } from 'fs';
import { resolve, dirname } from 'path';
import * as os from 'os';
import { ConfigManager } from '../config/config-manager.js';
import { providerFactory } from '../providers/provider-factory.js';
import { DefaultToolRegistry } from '../tools/tool-registry.js';
import { ReadFileTool } from '../tools/read-file.js';
import { WriteFileTool } from '../tools/write-file.js';
import { ShellTool } from '../tools/shell.js';
import { ListDirectoryTool } from '../tools/ls.js';
import { EditTool } from '../tools/edit.js';
import { GlobTool } from '../tools/glob.js';
import { GrepTool } from '../tools/grep.js';
import { ReadManyFilesTool } from '../tools/read-many-files.js';
import { WebFetchTool } from '../tools/web-fetch.js';
import { WebSearchTool } from '../tools/web-search.js';
import { MemoryTool } from '../tools/memoryTool.js';
import { MCPTool } from '../tools/mcp-tool.js';
import { CLIApprovalHandler } from './approval-handler.js';
import {
  AIProviderInterface,
  Message,
  ToolCall,
  ToolResult,
  RequestOptions,
  StreamChunk,
} from '../types/provider.js';
import {
  Tool,
  ToolExecutionContext,
  ToolExecutionResult,
} from '../types/tools.js';
import { Config } from '../types/config.js';
import {
  RetryPolicy,
  CircuitBreaker,
  retryPolicies,
  circuitBreakers,
  ErrorHandler,
  BaseError,
  NetworkError,
  TimeoutError,
  createErrorContext,
  wrapAsync,
} from '../utils/index.js';

export class CoreEngine {
  private configManager: ConfigManager;
  private toolRegistry: DefaultToolRegistry;
  private approvalHandler: CLIApprovalHandler;
  private provider?: AIProviderInterface;
  private conversationHistory: Message[] = [];
  private workingDirectory: string;
  private errorHandler: ErrorHandler;
  private retryPolicy: RetryPolicy;
  private circuitBreaker: CircuitBreaker;

  constructor() {
    this.configManager = new ConfigManager();
    this.approvalHandler = new CLIApprovalHandler();
    this.toolRegistry = new DefaultToolRegistry(this.approvalHandler);
    this.workingDirectory = process.cwd();
    this.errorHandler = ErrorHandler.getInstance();
    this.retryPolicy = retryPolicies.standard;
    this.circuitBreaker = circuitBreakers.standard;

    this.initializeTools();
    this.setupErrorHandling();
  }

  private initializeTools(): void {
    // Register built-in tools
    this.toolRegistry.register(new ReadFileTool());
    this.toolRegistry.register(new WriteFileTool());
    this.toolRegistry.register(new ShellTool());
    this.toolRegistry.register(new ListDirectoryTool());
    this.toolRegistry.register(new EditTool());
    this.toolRegistry.register(new GlobTool());
    this.toolRegistry.register(new GrepTool());
    this.toolRegistry.register(new ReadManyFilesTool());
    this.toolRegistry.register(new WebFetchTool());
    this.toolRegistry.register(new WebSearchTool());
    this.toolRegistry.register(new MemoryTool());
    this.toolRegistry.register(new MCPTool());
  }

  private setupErrorHandling(): void {
    // Add error listener for logging and monitoring
    this.errorHandler.addErrorListener((error: BaseError) => {
      console.error(`[${error.code}] ${error.message}`);

      // Log to file or monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        // TODO: Implement proper logging
      }
    });
  }

  async initialize(): Promise<void> {
    const config = await this.configManager.getConfig();
    if (!config) {
      throw new Error('No configuration found. Please run authentication first.');
    }

    // Initialize AI provider
    this.provider = providerFactory.create(config.provider);
    this.provider.configure({
      apiKey: config.apiKey,
      model: config.model,
      maxTokens: config.maxTokens,
      temperature: config.temperature,
      systemPrompt: config.systemPrompt,
    });

    // Load conversation history
    await this.loadHistory();
  }

  async processMessage(message: string): Promise<string> {
    await this.ensureInitialized();

    const context = createErrorContext('process_message', 'core_engine');

    return this.circuitBreaker.execute(async () => {
      return this.retryPolicy.execute(async () => {
        try {
          // Add user message to history
          const userMessage: Message = {
            role: 'user',
            content: message,
            timestamp: new Date(),
          };
          this.conversationHistory.push(userMessage);

          // Prepare request options
          const requestOptions: RequestOptions = {
            messages: this.conversationHistory,
            tools: this.getToolDefinitions(),
            stream: false,
          };

          const response = await this.provider!.request(requestOptions);

          // Handle tool calls if any
          if (response.toolCalls && response.toolCalls.length > 0) {
            const toolResults = await this.executeToolCalls(response.toolCalls);

            // Add assistant message with tool calls
            this.conversationHistory.push({
              role: 'assistant',
              content: response.content,
              timestamp: new Date(),
            });

            // Add tool results and get final response
            const finalResponse = await this.provider!.request({
              ...requestOptions,
              messages: this.conversationHistory,
              toolResults,
            });

            // Add final assistant message
            const assistantMessage: Message = {
              role: 'assistant',
              content: finalResponse.content,
              timestamp: new Date(),
            };
            this.conversationHistory.push(assistantMessage);

            await this.saveHistory();
            return finalResponse.content;
          } else {
            // No tool calls, just add response to history
            const assistantMessage: Message = {
              role: 'assistant',
              content: response.content,
              timestamp: new Date(),
            };
            this.conversationHistory.push(assistantMessage);

            await this.saveHistory();
            return response.content;
          }
        } catch (error) {
          const handledError = this.errorHandler.handle(error as Error, context);
          throw handledError;
        }
      });
    });
  }

  async streamResponse(message: string, onChunk: (chunk: string) => void): Promise<void> {
    await this.ensureInitialized();

    const context = createErrorContext('stream_response', 'core_engine');

    try {
      // Add user message to history
      const userMessage: Message = {
        role: 'user',
        content: message,
        timestamp: new Date(),
      };
      this.conversationHistory.push(userMessage);

      // Prepare request options
      const requestOptions: RequestOptions = {
        messages: this.conversationHistory,
        tools: this.getToolDefinitions(),
        stream: true,
      };

      let fullContent = '';
      let toolCalls: ToolCall[] = [];

      for await (const chunk of this.provider!.stream(requestOptions)) {
        if (chunk.content) {
          fullContent += chunk.content;
          onChunk(chunk.content);
        }

        if (chunk.toolCalls) {
          toolCalls = chunk.toolCalls;
        }

        if (chunk.finished) {
          // Handle tool calls if any
          if (toolCalls.length > 0) {
            onChunk('\n\n🔧 Executing tools...\n');

            const toolResults = await this.executeToolCallsWithRetry(toolCalls);

            // Add assistant message with tool calls
            this.conversationHistory.push({
              role: 'assistant',
              content: fullContent,
              timestamp: new Date(),
            });

            // Get final response with tool results
            const finalRequestOptions: RequestOptions = {
              messages: this.conversationHistory,
              tools: this.getToolDefinitions(),
              toolResults,
              stream: true,
            };

            let finalContent = '';
            for await (const finalChunk of this.provider!.stream(finalRequestOptions)) {
              if (finalChunk.content) {
                finalContent += finalChunk.content;
                onChunk(finalChunk.content);
              }

              if (finalChunk.finished) {
                // Add final assistant message
                this.conversationHistory.push({
                  role: 'assistant',
                  content: finalContent,
                  timestamp: new Date(),
                });
                break;
              }

              if (finalChunk.error) {
                throw new Error(finalChunk.error);
              }
            }
          } else {
            // No tool calls, just add response to history
            this.conversationHistory.push({
              role: 'assistant',
              content: fullContent,
              timestamp: new Date(),
            });
          }

          await this.saveHistory();
          break;
        }

        if (chunk.error) {
          throw new Error(chunk.error);
        }
      }
    } catch (error) {
      const handledError = this.errorHandler.handle(error as Error, context);
      onChunk(`\n\n❌ Error: ${handledError.userMessage || handledError.message}\n`);
      throw handledError;
    }
  }

  private async executeToolCalls(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];
    const context = this.getToolExecutionContext();

    for (const toolCall of toolCalls) {
      try {
        const result = await this.toolRegistry.execute(
          toolCall.name,
          toolCall.parameters,
          context
        );

        results.push({
          id: toolCall.id,
          result: result.success ? result.result : { error: result.error },
        });
      } catch (error) {
        results.push({
          id: toolCall.id,
          result: { error: error instanceof Error ? error.message : String(error) },
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return results;
  }

  private async executeToolCallsWithRetry(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const context = createErrorContext('execute_tool_calls', 'core_engine');

    return this.retryPolicy.execute(async () => {
      try {
        return await this.executeToolCalls(toolCalls);
      } catch (error) {
        const handledError = this.errorHandler.handle(error as Error, context);
        throw handledError;
      }
    });
  }

  private getToolDefinitions() {
    const context = this.getToolExecutionContext();
    return this.toolRegistry.getAvailable(context).map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
    }));
  }

  private getToolExecutionContext(): ToolExecutionContext {
    const config = this.configManager.exportConfig().config;
    
    return {
      workingDirectory: this.workingDirectory,
      approvalLevel: config.approvalLevel || 'default',
      allowedCommands: [],
      deniedCommands: [],
      autoApprovePatterns: [],
    };
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.provider) {
      await this.initialize();
    }
  }

  // History management
  async getHistory(): Promise<Message[]> {
    return [...this.conversationHistory];
  }

  async clearHistory(): Promise<void> {
    this.conversationHistory = [];
    await this.saveHistory();
  }

  private async loadHistory(): Promise<void> {
    try {
      const config = this.configManager.getConfig();
      const historyPath = this.getHistoryPath();

      if (await this.fileExists(historyPath)) {
        const historyData = await fs.readFile(historyPath, 'utf8');
        const parsed = JSON.parse(historyData);

        // Validate and restore history
        if (Array.isArray(parsed.messages)) {
          this.conversationHistory = parsed.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          }));
        }
      } else {
        this.conversationHistory = [];
      }
    } catch (error) {
      console.warn('Failed to load conversation history:', error);
      this.conversationHistory = [];
    }
  }

  private async saveHistory(): Promise<void> {
    try {
      const historyPath = this.getHistoryPath();
      const historyDir = dirname(historyPath);

      // Ensure directory exists
      await fs.mkdir(historyDir, { recursive: true });

      const config = await this.configManager.getConfig();
      const historyData = {
        version: '1.0',
        savedAt: new Date().toISOString(),
        messages: this.conversationHistory,
        metadata: {
          provider: config?.provider,
          model: config?.model,
          workingDirectory: this.workingDirectory,
        },
      };

      await fs.writeFile(historyPath, JSON.stringify(historyData, null, 2), 'utf8');
    } catch (error) {
      console.warn('Failed to save conversation history:', error);
    }
  }

  private getHistoryPath(): string {
    const sessionId = this.generateSessionId();
    const historyDir = resolve(os.homedir(), '.arien-ai', 'history');
    return resolve(historyDir, `session-${sessionId}.json`);
  }

  private generateSessionId(): string {
    // Generate a session ID based on date and working directory
    const date = new Date().toISOString().split('T')[0];
    const dirHash = require('crypto')
      .createHash('md5')
      .update(this.workingDirectory)
      .digest('hex')
      .substring(0, 8);
    return `${date}-${dirHash}`;
  }

  private async fileExists(path: string): Promise<boolean> {
    try {
      await fs.access(path);
      return true;
    } catch {
      return false;
    }
  }

  // Tool management
  getAvailableTools(): Tool[] {
    const context = this.getToolExecutionContext();
    return this.toolRegistry.getAvailable(context);
  }

  registerTool(tool: Tool): void {
    this.toolRegistry.register(tool);
  }

  // Configuration
  setWorkingDirectory(directory: string): void {
    this.workingDirectory = directory;
  }

  getWorkingDirectory(): string {
    return this.workingDirectory;
  }
}
