import OpenAI from 'openai';
import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  RequestOptions,
  ProviderResponse,
  StreamChunk,
  ToolCall,
  ProviderError,
  AuthenticationError,
  RateLimitError,
  ModelNotFoundError,
  ContentFilterError,
  TokenLimitError,
} from '../types/provider.js';

export class DeepseekProvider extends BaseProvider {
  private client?: OpenAI;

  constructor() {
    super('deepseek');
  }

  configure(config: ProviderConfig): void {
    super.configure(config);
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl || 'https://api.deepseek.com',
    });
  }

  async request(options: RequestOptions): Promise<ProviderResponse> {
    this.ensureConfigured();
    this.validateRequestOptions(options);

    const mergedOptions = this.addSystemPrompt(this.mergeConfig(options));

    return this.retryWithBackoff(async () => {
      try {
        const response = await this.client!.chat.completions.create({
          model: this.config!.model,
          messages: mergedOptions.messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          tools: mergedOptions.tools?.map(tool => ({
            type: 'function' as const,
            function: {
              name: tool.name,
              description: tool.description,
              parameters: tool.parameters,
            },
          })),
          tool_choice: mergedOptions.tools ? 'auto' : undefined,
          max_tokens: mergedOptions.maxTokens,
          temperature: mergedOptions.temperature,
          top_p: mergedOptions.topP,
          frequency_penalty: mergedOptions.frequencyPenalty,
          presence_penalty: mergedOptions.presencePenalty,
          stop: mergedOptions.stop,
          stream: false,
        });

        const choice = response.choices[0];
        if (!choice) {
          throw new ProviderError('No response from Deepseek', 'NO_RESPONSE', this.name);
        }

        const toolCalls: ToolCall[] = choice.message.tool_calls?.map(tc => ({
          id: tc.id,
          name: tc.function.name,
          parameters: JSON.parse(tc.function.arguments),
        })) || [];

        return {
          content: choice.message.content || '',
          toolCalls,
          usage: response.usage ? {
            promptTokens: response.usage.prompt_tokens,
            completionTokens: response.usage.completion_tokens,
            totalTokens: response.usage.total_tokens,
          } : undefined,
          finishReason: this.mapFinishReason(choice.finish_reason),
        };
      } catch (error) {
        this.handleDeepseekError(error);
      }
    });
  }

  async *stream(options: RequestOptions): AsyncGenerator<StreamChunk, void, unknown> {
    this.ensureConfigured();
    this.validateRequestOptions(options);

    const mergedOptions = this.addSystemPrompt(this.mergeConfig(options));

    try {
      const stream = await this.client!.chat.completions.create({
        model: this.config!.model,
        messages: mergedOptions.messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        tools: mergedOptions.tools?.map(tool => ({
          type: 'function' as const,
          function: {
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters,
          },
        })),
        tool_choice: mergedOptions.tools ? 'auto' : undefined,
        max_tokens: mergedOptions.maxTokens,
        temperature: mergedOptions.temperature,
        top_p: mergedOptions.topP,
        frequency_penalty: mergedOptions.frequencyPenalty,
        presence_penalty: mergedOptions.presencePenalty,
        stop: mergedOptions.stop,
        stream: true,
      });

      let toolCalls: ToolCall[] = [];
      const toolCallsMap = new Map<string, Partial<ToolCall>>();

      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        if (!choice) continue;

        const delta = choice.delta;

        // Handle content
        if (delta.content) {
          yield { content: delta.content };
        }

        // Handle tool calls
        if (delta.tool_calls) {
          for (const toolCall of delta.tool_calls) {
            const id = toolCall.id || '';
            if (!toolCallsMap.has(id)) {
              toolCallsMap.set(id, { id, name: '', parameters: {} });
            }

            const existing = toolCallsMap.get(id)!;
            if (toolCall.function?.name) {
              existing.name = toolCall.function.name;
            }
            if (toolCall.function?.arguments) {
              try {
                existing.parameters = JSON.parse(toolCall.function.arguments);
              } catch {
                // Partial arguments, continue accumulating
              }
            }
          }
        }

        // Check if finished
        if (choice.finish_reason) {
          // Convert accumulated tool calls
          toolCalls = Array.from(toolCallsMap.values())
            .filter(tc => tc.id && tc.name)
            .map(tc => tc as ToolCall);

          yield {
            toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
            finished: true,
          };
          return;
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      yield { error: errorMessage, finished: true };
    }
  }

  async validateConfig(config: ProviderConfig): Promise<boolean> {
    try {
      const client = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseUrl || 'https://api.deepseek.com',
      });

      await client.models.list();
      return true;
    } catch (error) {
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    this.ensureConfigured();

    try {
      const response = await this.client!.models.list();
      return response.data
        .filter(model => model.id.includes('deepseek'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      // Fallback to known models if API call fails
      return ['deepseek-chat', 'deepseek-coder', 'deepseek-reasoner'];
    }
  }

  async getModelInfo(model: string): Promise<{
    name: string;
    maxTokens: number;
    supportsTools: boolean;
    supportsStreaming: boolean;
  }> {
    const modelInfo = {
      'deepseek-chat': { maxTokens: 32768, supportsTools: true, supportsStreaming: true },
      'deepseek-coder': { maxTokens: 32768, supportsTools: true, supportsStreaming: true },
      'deepseek-reasoner': { maxTokens: 32768, supportsTools: true, supportsStreaming: true },
      'deepseek-v2': { maxTokens: 32768, supportsTools: true, supportsStreaming: true },
      'deepseek-v2.5': { maxTokens: 32768, supportsTools: true, supportsStreaming: true },
    };

    const info = modelInfo[model as keyof typeof modelInfo];
    if (!info) {
      throw new ModelNotFoundError(this.name, model);
    }

    return {
      name: model,
      ...info,
    };
  }

  private handleDeepseekError(error: any): never {
    if (error?.status) {
      const message = error.message || 'Unknown Deepseek error';
      
      switch (error.status) {
        case 401:
          throw new AuthenticationError(this.name, message);
        case 429:
          throw new RateLimitError(this.name, error.headers?.['retry-after']);
        case 400:
          if (message.includes('content_filter')) {
            throw new ContentFilterError(this.name, message);
          }
          if (message.includes('token')) {
            throw new TokenLimitError(this.name, this.config?.maxTokens || 4096);
          }
          break;
        case 404:
          throw new ModelNotFoundError(this.name, this.config?.model || 'unknown');
      }
      
      this.handleHttpError(error.status, message);
    }

    throw new ProviderError(
      error.message || 'Unknown Deepseek error',
      'UNKNOWN_ERROR',
      this.name
    );
  }

  private mapFinishReason(reason: string | null): 'stop' | 'length' | 'tool_calls' | 'content_filter' {
    switch (reason) {
      case 'stop':
        return 'stop';
      case 'length':
        return 'length';
      case 'tool_calls':
        return 'tool_calls';
      case 'content_filter':
        return 'content_filter';
      default:
        return 'stop';
    }
  }
}
