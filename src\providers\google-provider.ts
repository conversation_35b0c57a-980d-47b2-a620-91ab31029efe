import { GoogleGenerativeAI, GenerativeModel, ChatSession } from '@google/generative-ai';
import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  RequestOptions,
  ProviderResponse,
  StreamChunk,
  ToolCall,
  Message,
  ProviderError,
  AuthenticationError,
  RateLimitError,
  ModelNotFoundError,
  ContentFilterError,
  TokenLimitError,
} from '../types/provider.js';

export class GoogleProvider extends BaseProvider {
  private client?: GoogleGenerativeAI;
  private model?: GenerativeModel;

  constructor() {
    super('google');
  }

  configure(config: ProviderConfig): void {
    super.configure(config);
    this.client = new GoogleGenerativeAI(config.apiKey);
    this.model = this.client.getGenerativeModel({ 
      model: config.model,
      generationConfig: {
        maxOutputTokens: config.maxTokens,
        temperature: config.temperature,
        topP: config.topP,
      },
    });
  }

  async request(options: RequestOptions): Promise<ProviderResponse> {
    this.ensureConfigured();
    this.validateRequestOptions(options);

    const mergedOptions = this.addSystemPrompt(this.mergeConfig(options));

    return this.retryWithBackoff(async () => {
      try {
        const prompt = this.formatMessagesForGoogle(mergedOptions.messages);
        
        // Handle tools if provided
        if (mergedOptions.tools && mergedOptions.tools.length > 0) {
          const tools = this.formatToolsForGoogle(mergedOptions.tools);
          const modelWithTools = this.client!.getGenerativeModel({
            model: this.config!.model,
            tools: [{ functionDeclarations: tools }],
            generationConfig: {
              maxOutputTokens: mergedOptions.maxTokens,
              temperature: mergedOptions.temperature,
              topP: mergedOptions.topP,
            },
          });

          const result = await modelWithTools.generateContent(prompt);
          const response = result.response;
          
          // Extract tool calls
          const toolCalls: ToolCall[] = [];
          const functionCalls = response.functionCalls();
          if (functionCalls) {
            for (const call of functionCalls) {
              toolCalls.push({
                id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                name: call.name,
                parameters: call.args || {},
              });
            }
          }

          return {
            content: response.text() || '',
            toolCalls,
            usage: {
              promptTokens: result.response.usageMetadata?.promptTokenCount || 0,
              completionTokens: result.response.usageMetadata?.candidatesTokenCount || 0,
              totalTokens: result.response.usageMetadata?.totalTokenCount || 0,
            },
            finishReason: this.mapGoogleFinishReason(response.candidates?.[0]?.finishReason),
          };
        } else {
          const result = await this.model!.generateContent(prompt);
          const response = result.response;

          return {
            content: response.text() || '',
            toolCalls: [],
            usage: {
              promptTokens: result.response.usageMetadata?.promptTokenCount || 0,
              completionTokens: result.response.usageMetadata?.candidatesTokenCount || 0,
              totalTokens: result.response.usageMetadata?.totalTokenCount || 0,
            },
            finishReason: this.mapGoogleFinishReason(response.candidates?.[0]?.finishReason),
          };
        }
      } catch (error) {
        this.handleGoogleError(error);
      }
    });
  }

  async *stream(options: RequestOptions): AsyncGenerator<StreamChunk, void, unknown> {
    this.ensureConfigured();
    this.validateRequestOptions(options);

    const mergedOptions = this.addSystemPrompt(this.mergeConfig(options));

    try {
      const prompt = this.formatMessagesForGoogle(mergedOptions.messages);
      
      // Handle tools if provided
      if (mergedOptions.tools && mergedOptions.tools.length > 0) {
        const tools = this.formatToolsForGoogle(mergedOptions.tools);
        const modelWithTools = this.client!.getGenerativeModel({
          model: this.config!.model,
          tools: [{ functionDeclarations: tools }],
          generationConfig: {
            maxOutputTokens: mergedOptions.maxTokens,
            temperature: mergedOptions.temperature,
            topP: mergedOptions.topP,
          },
        });

        const result = await modelWithTools.generateContentStream(prompt);
        
        let toolCalls: ToolCall[] = [];
        for await (const chunk of result.stream) {
          const chunkText = chunk.text();
          if (chunkText) {
            yield { content: chunkText };
          }

          // Check for function calls
          const functionCalls = chunk.functionCalls();
          if (functionCalls) {
            for (const call of functionCalls) {
              toolCalls.push({
                id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                name: call.name,
                parameters: call.args || {},
              });
            }
          }
        }

        if (toolCalls.length > 0) {
          yield { toolCalls, finished: true };
        } else {
          yield { finished: true };
        }
      } else {
        const result = await this.model!.generateContentStream(prompt);
        
        for await (const chunk of result.stream) {
          const chunkText = chunk.text();
          if (chunkText) {
            yield { content: chunkText };
          }
        }
        
        yield { finished: true };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      yield { error: errorMessage, finished: true };
    }
  }

  async validateConfig(config: ProviderConfig): Promise<boolean> {
    try {
      const client = new GoogleGenerativeAI(config.apiKey);
      const model = client.getGenerativeModel({ model: config.model });
      
      // Test with a simple prompt
      await model.generateContent('Hello');
      return true;
    } catch (error) {
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    // Google doesn't provide a direct API to list models, so we return known models
    return [
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-1.0-pro',
      'gemini-1.0-pro-vision',
    ];
  }

  async getModelInfo(model: string): Promise<{
    name: string;
    maxTokens: number;
    supportsTools: boolean;
    supportsStreaming: boolean;
  }> {
    const modelInfo = {
      'gemini-1.5-pro': { maxTokens: 1048576, supportsTools: true, supportsStreaming: true },
      'gemini-1.5-flash': { maxTokens: 1048576, supportsTools: true, supportsStreaming: true },
      'gemini-1.0-pro': { maxTokens: 32768, supportsTools: true, supportsStreaming: true },
      'gemini-1.0-pro-vision': { maxTokens: 32768, supportsTools: false, supportsStreaming: true },
    };

    const info = modelInfo[model as keyof typeof modelInfo];
    if (!info) {
      throw new ModelNotFoundError(this.name, model);
    }

    return {
      name: model,
      ...info,
    };
  }

  private formatMessagesForGoogle(messages: Message[]): string {
    // Google Gemini expects a single prompt string
    // We'll format the conversation as a single prompt
    let prompt = '';
    
    for (const message of messages) {
      switch (message.role) {
        case 'system':
          prompt += `System: ${message.content}\n\n`;
          break;
        case 'user':
          prompt += `User: ${message.content}\n\n`;
          break;
        case 'assistant':
          prompt += `Assistant: ${message.content}\n\n`;
          break;
      }
    }
    
    return prompt.trim();
  }

  private formatToolsForGoogle(tools: any[]) {
    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
    }));
  }

  private handleGoogleError(error: any): never {
    const message = error.message || 'Unknown Google error';
    
    if (message.includes('API_KEY_INVALID') || message.includes('authentication')) {
      throw new AuthenticationError(this.name, message);
    }
    
    if (message.includes('RATE_LIMIT') || message.includes('quota')) {
      throw new RateLimitError(this.name);
    }
    
    if (message.includes('MODEL_NOT_FOUND')) {
      throw new ModelNotFoundError(this.name, this.config?.model || 'unknown');
    }
    
    if (message.includes('SAFETY') || message.includes('content_filter')) {
      throw new ContentFilterError(this.name, message);
    }
    
    if (message.includes('token') || message.includes('length')) {
      throw new TokenLimitError(this.name, this.config?.maxTokens || 4096);
    }

    throw new ProviderError(message, 'UNKNOWN_ERROR', this.name);
  }

  private mapGoogleFinishReason(reason: string | undefined): 'stop' | 'length' | 'tool_calls' | 'content_filter' {
    switch (reason) {
      case 'STOP':
        return 'stop';
      case 'MAX_TOKENS':
        return 'length';
      case 'SAFETY':
        return 'content_filter';
      case 'RECITATION':
        return 'content_filter';
      default:
        return 'stop';
    }
  }
}
