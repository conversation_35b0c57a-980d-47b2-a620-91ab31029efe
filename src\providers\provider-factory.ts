import { AIProviderInterface, ProviderFactory } from '../types/provider.js';
import { OpenAIProvider } from './openai-provider.js';
import { AnthropicProvider } from './anthropic-provider.js';
import { DeepseekProvider } from './deepseek-provider.js';
import { GoogleProvider } from './google-provider.js';
import { AIProvider } from '../types/config.js';

// Provider implementations are now in separate files

export class DefaultProviderFactory implements ProviderFactory {
  private providers: Map<string, () => AIProviderInterface> = new Map();

  constructor() {
    this.registerProviders();
  }

  private registerProviders(): void {
    this.providers.set(AIProvider.OPENAI, () => new OpenAIProvider());
    this.providers.set(AIProvider.ANTHROPIC, () => new AnthropicProvider());
    this.providers.set(AIProvider.DEEPSEEK, () => new DeepseekProvider());
    this.providers.set(AIProvider.GOOGLE, () => new GoogleProvider());
  }

  create(providerName: string): AIProviderInterface {
    const providerFactory = this.providers.get(providerName);
    if (!providerFactory) {
      throw new Error(`Unsupported provider: ${providerName}`);
    }
    return providerFactory();
  }

  getSupportedProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  registerProvider(name: string, factory: () => AIProviderInterface): void {
    this.providers.set(name, factory);
  }

  unregisterProvider(name: string): void {
    this.providers.delete(name);
  }
}

// Singleton instance
export const providerFactory = new DefaultProviderFactory();
