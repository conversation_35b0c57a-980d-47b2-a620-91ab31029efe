import chalk from 'chalk';

export interface DiffOptions {
  context: number;
  ignoreWhitespace: boolean;
  ignoreCase: boolean;
  showLineNumbers: boolean;
  colorOutput: boolean;
  unified: boolean;
  wordDiff: boolean;
  maxLines: number;
}

export interface DiffLine {
  type: 'add' | 'remove' | 'context' | 'header';
  content: string;
  lineNumber?: {
    old?: number;
    new?: number;
  };
}

export interface DiffResult {
  lines: DiffLine[];
  stats: {
    additions: number;
    deletions: number;
    changes: number;
  };
  truncated: boolean;
}

export class DiffFormatter {
  private options: DiffOptions;

  constructor(options: Partial<DiffOptions> = {}) {
    this.options = {
      context: 3,
      ignoreWhitespace: false,
      ignoreCase: false,
      showLineNumbers: true,
      colorOutput: true,
      unified: true,
      wordDiff: false,
      maxLines: 1000,
      ...options,
    };
  }

  /**
   * Generate a diff between two strings
   */
  generateDiff(oldContent: string, newContent: string, oldPath?: string, newPath?: string): DiffResult {
    const oldLines = this.preprocessLines(oldContent.split('\n'));
    const newLines = this.preprocessLines(newContent.split('\n'));

    const diffLines: DiffLine[] = [];
    const stats = { additions: 0, deletions: 0, changes: 0 };

    // Add header
    if (oldPath || newPath) {
      diffLines.push({
        type: 'header',
        content: `--- ${oldPath || 'a/file'}`,
      });
      diffLines.push({
        type: 'header',
        content: `+++ ${newPath || 'b/file'}`,
      });
    }

    // Generate unified diff
    const hunks = this.generateHunks(oldLines, newLines);
    
    for (const hunk of hunks) {
      // Add hunk header
      diffLines.push({
        type: 'header',
        content: `@@ -${hunk.oldStart},${hunk.oldCount} +${hunk.newStart},${hunk.newCount} @@`,
      });

      // Add hunk lines
      for (const line of hunk.lines) {
        diffLines.push(line);
        
        switch (line.type) {
          case 'add':
            stats.additions++;
            break;
          case 'remove':
            stats.deletions++;
            break;
        }
      }

      // Check if we've exceeded max lines
      if (diffLines.length >= this.options.maxLines) {
        break;
      }
    }

    stats.changes = Math.max(stats.additions, stats.deletions);

    return {
      lines: diffLines.slice(0, this.options.maxLines),
      stats,
      truncated: diffLines.length > this.options.maxLines,
    };
  }

  /**
   * Format diff result as a string
   */
  formatDiff(diffResult: DiffResult): string {
    const lines = diffResult.lines.map(line => this.formatLine(line));
    
    let result = lines.join('\n');
    
    if (diffResult.truncated) {
      result += '\n... (diff truncated)';
    }

    return result;
  }

  /**
   * Generate a summary of changes
   */
  generateSummary(diffResult: DiffResult): string {
    const { stats } = diffResult;
    const parts = [];

    if (stats.additions > 0) {
      parts.push(`${stats.additions} addition${stats.additions !== 1 ? 's' : ''}`);
    }
    
    if (stats.deletions > 0) {
      parts.push(`${stats.deletions} deletion${stats.deletions !== 1 ? 's' : ''}`);
    }

    if (parts.length === 0) {
      return 'No changes';
    }

    return parts.join(', ');
  }

  private preprocessLines(lines: string[]): string[] {
    if (this.options.ignoreWhitespace) {
      lines = lines.map(line => line.trim());
    }
    
    if (this.options.ignoreCase) {
      lines = lines.map(line => line.toLowerCase());
    }

    return lines;
  }

  private generateHunks(oldLines: string[], newLines: string[]): any[] {
    const hunks = [];
    const lcs = this.longestCommonSubsequence(oldLines, newLines);
    
    let oldIndex = 0;
    let newIndex = 0;
    let lcsIndex = 0;

    while (oldIndex < oldLines.length || newIndex < newLines.length) {
      const hunk = {
        oldStart: oldIndex + 1,
        newStart: newIndex + 1,
        oldCount: 0,
        newCount: 0,
        lines: [] as DiffLine[],
      };

      // Add context before changes
      const contextStart = Math.max(0, oldIndex - this.options.context);
      for (let i = contextStart; i < oldIndex; i++) {
        if (hunk.lines.length < this.options.context) {
          hunk.lines.push({
            type: 'context',
            content: ` ${oldLines[i]}`,
            lineNumber: this.options.showLineNumbers ? { old: i + 1, new: i + 1 } : undefined,
          });
        }
      }

      // Process changes
      while (oldIndex < oldLines.length || newIndex < newLines.length) {
        if (lcsIndex < lcs.length && 
            oldIndex < oldLines.length && 
            newIndex < newLines.length &&
            oldLines[oldIndex] === lcs[lcsIndex] && 
            newLines[newIndex] === lcs[lcsIndex]) {
          // Common line
          hunk.lines.push({
            type: 'context',
            content: ` ${oldLines[oldIndex]}`,
            lineNumber: this.options.showLineNumbers ? { old: oldIndex + 1, new: newIndex + 1 } : undefined,
          });
          oldIndex++;
          newIndex++;
          lcsIndex++;
          hunk.oldCount++;
          hunk.newCount++;
        } else if (oldIndex < oldLines.length && 
                   (newIndex >= newLines.length || 
                    (lcsIndex < lcs.length && oldLines[oldIndex] !== lcs[lcsIndex]))) {
          // Deleted line
          hunk.lines.push({
            type: 'remove',
            content: `-${oldLines[oldIndex]}`,
            lineNumber: this.options.showLineNumbers ? { old: oldIndex + 1 } : undefined,
          });
          oldIndex++;
          hunk.oldCount++;
        } else if (newIndex < newLines.length) {
          // Added line
          hunk.lines.push({
            type: 'add',
            content: `+${newLines[newIndex]}`,
            lineNumber: this.options.showLineNumbers ? { new: newIndex + 1 } : undefined,
          });
          newIndex++;
          hunk.newCount++;
        }

        // Check if we should start a new hunk
        if (this.shouldStartNewHunk(oldIndex, newIndex, oldLines, newLines, lcs, lcsIndex)) {
          break;
        }
      }

      // Add context after changes
      const contextEnd = Math.min(oldLines.length, oldIndex + this.options.context);
      for (let i = oldIndex; i < contextEnd; i++) {
        hunk.lines.push({
          type: 'context',
          content: ` ${oldLines[i]}`,
          lineNumber: this.options.showLineNumbers ? { old: i + 1, new: i + 1 } : undefined,
        });
        hunk.oldCount++;
        hunk.newCount++;
      }

      if (hunk.lines.length > 0) {
        hunks.push(hunk);
      }
    }

    return hunks;
  }

  private shouldStartNewHunk(
    oldIndex: number,
    newIndex: number,
    oldLines: string[],
    newLines: string[],
    lcs: string[],
    lcsIndex: number
  ): boolean {
    // Simple heuristic: start new hunk if we have enough context
    let contextCount = 0;
    let tempOldIndex = oldIndex;
    let tempNewIndex = newIndex;
    let tempLcsIndex = lcsIndex;

    while (tempOldIndex < oldLines.length && 
           tempNewIndex < newLines.length && 
           tempLcsIndex < lcs.length &&
           oldLines[tempOldIndex] === lcs[tempLcsIndex] && 
           newLines[tempNewIndex] === lcs[tempLcsIndex]) {
      contextCount++;
      tempOldIndex++;
      tempNewIndex++;
      tempLcsIndex++;

      if (contextCount >= this.options.context * 2) {
        return true;
      }
    }

    return false;
  }

  private longestCommonSubsequence(a: string[], b: string[]): string[] {
    const m = a.length;
    const n = b.length;
    const dp = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));

    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (a[i - 1] === b[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }

    // Reconstruct LCS
    const lcs = [];
    let i = m;
    let j = n;

    while (i > 0 && j > 0) {
      if (a[i - 1] === b[j - 1]) {
        lcs.unshift(a[i - 1]);
        i--;
        j--;
      } else if (dp[i - 1][j] > dp[i][j - 1]) {
        i--;
      } else {
        j--;
      }
    }

    return lcs;
  }

  private formatLine(line: DiffLine): string {
    let formatted = line.content;

    if (this.options.colorOutput) {
      switch (line.type) {
        case 'add':
          formatted = chalk.green(formatted);
          break;
        case 'remove':
          formatted = chalk.red(formatted);
          break;
        case 'header':
          formatted = chalk.cyan(formatted);
          break;
        case 'context':
          // No color for context lines
          break;
      }
    }

    if (this.options.showLineNumbers && line.lineNumber) {
      const oldNum = line.lineNumber.old ? line.lineNumber.old.toString().padStart(4) : '    ';
      const newNum = line.lineNumber.new ? line.lineNumber.new.toString().padStart(4) : '    ';
      formatted = `${oldNum} ${newNum} ${formatted}`;
    }

    return formatted;
  }
}

export const defaultDiffOptions: DiffOptions = {
  context: 3,
  ignoreWhitespace: false,
  ignoreCase: false,
  showLineNumbers: true,
  colorOutput: true,
  unified: true,
  wordDiff: false,
  maxLines: 1000,
};
