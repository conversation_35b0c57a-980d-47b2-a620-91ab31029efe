import { promises as fs } from 'fs';
import { ModifiableTool } from './modifiable-tool.js';
import {
  ToolExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';

interface EditOperation {
  type: 'replace' | 'insert' | 'delete' | 'append' | 'prepend';
  startLine?: number;
  endLine?: number;
  content?: string;
  searchText?: string;
  replaceText?: string;
}

export class EditTool extends ModifiableTool {
  name = 'edit_file';
  description = 'Edit a file with intelligent diff application, supporting various edit operations';

  parameters = {
    type: 'object' as const,
    properties: {
      path: {
        type: 'string',
        description: 'The path to the file to edit (relative to working directory)',
      },
      operation: {
        type: 'object',
        description: 'The edit operation to perform',
        properties: {
          type: {
            type: 'string',
            enum: ['replace', 'insert', 'delete', 'append', 'prepend'],
            description: 'Type of edit operation',
          },
          startLine: {
            type: 'number',
            description: 'Starting line number (1-based) for line-based operations',
          },
          endLine: {
            type: 'number',
            description: 'Ending line number (1-based) for range operations',
          },
          content: {
            type: 'string',
            description: 'Content to insert, append, or prepend',
          },
          searchText: {
            type: 'string',
            description: 'Text to search for in replace operations',
          },
          replaceText: {
            type: 'string',
            description: 'Text to replace with in replace operations',
          },
        },
        required: ['type'],
      },
      createBackup: {
        type: 'boolean',
        description: 'Whether to create a backup before editing (default: true)',
        default: true,
      },
      encoding: {
        type: 'string',
        description: 'File encoding (default: utf8)',
        enum: ['utf8', 'ascii', 'latin1'],
        default: 'utf8',
      },
    },
    required: ['path', 'operation'],
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const { 
      path, 
      operation, 
      createBackup = true, 
      encoding = 'utf8' 
    } = parameters;

    try {
      const fullPath = this.validatePath(path, context);
      
      // Validate operation
      const editOp = this.validateOperation(operation);
      
      // Check if file exists
      if (!(await this.fileExists(fullPath))) {
        return this.createErrorResult(`File not found: ${path}`);
      }

      // Validate file extension
      if (!this.isAllowedExtension(fullPath)) {
        return this.createErrorResult(`File type not allowed for editing: ${path}`);
      }

      // Read current content
      const originalContent = await fs.readFile(fullPath, encoding);
      const lines = originalContent.split('\n');

      // Create backup if requested
      let backupPath: string | undefined;
      if (createBackup) {
        backupPath = await this.createBackup(fullPath);
      }

      // Apply the edit operation
      const newContent = await this.applyEdit(lines, editOp);
      
      // Validate the new content
      const sanitizedContent = this.sanitizeContent(newContent);

      // Write the modified content
      await fs.writeFile(fullPath, sanitizedContent, encoding);

      // Calculate diff stats
      const originalLines = lines.length;
      const newLines = sanitizedContent.split('\n').length;
      const linesChanged = Math.abs(newLines - originalLines);

      return this.createSuccessResult(
        {
          path: fullPath,
          operation: editOp.type,
          originalLines,
          newLines,
          linesChanged,
          backupPath,
          preview: this.generatePreview(originalContent, sanitizedContent),
        },
        'edit',
        {
          filePath: fullPath,
          operationType: editOp.type,
          backupCreated: !!backupPath,
        }
      );

    } catch (error) {
      return this.handleFileError(error, 'File edit', path);
    }
  }

  private validateOperation(operation: any): EditOperation {
    if (!operation || typeof operation !== 'object') {
      throw new ToolValidationError(
        'Operation must be an object',
        'INVALID_OPERATION',
        this.name
      );
    }

    const { type } = operation;
    if (!['replace', 'insert', 'delete', 'append', 'prepend'].includes(type)) {
      throw new ToolValidationError(
        `Invalid operation type: ${type}`,
        'INVALID_OPERATION_TYPE',
        this.name
      );
    }

    // Validate required fields based on operation type
    switch (type) {
      case 'replace':
        if (!operation.searchText || operation.replaceText === undefined) {
          throw new ToolValidationError(
            'Replace operation requires searchText and replaceText',
            'MISSING_REPLACE_PARAMS',
            this.name
          );
        }
        break;
      case 'insert':
        if (operation.startLine === undefined || !operation.content) {
          throw new ToolValidationError(
            'Insert operation requires startLine and content',
            'MISSING_INSERT_PARAMS',
            this.name
          );
        }
        break;
      case 'delete':
        if (operation.startLine === undefined) {
          throw new ToolValidationError(
            'Delete operation requires startLine',
            'MISSING_DELETE_PARAMS',
            this.name
          );
        }
        break;
      case 'append':
      case 'prepend':
        if (!operation.content) {
          throw new ToolValidationError(
            `${type} operation requires content`,
            'MISSING_CONTENT',
            this.name
          );
        }
        break;
    }

    return operation as EditOperation;
  }

  private async applyEdit(lines: string[], operation: EditOperation): Promise<string> {
    const result = [...lines];

    switch (operation.type) {
      case 'replace':
        return this.applyReplace(result.join('\n'), operation);
      
      case 'insert':
        return this.applyInsert(result, operation);
      
      case 'delete':
        return this.applyDelete(result, operation);
      
      case 'append':
        result.push(operation.content!);
        return result.join('\n');
      
      case 'prepend':
        result.unshift(operation.content!);
        return result.join('\n');
      
      default:
        throw new Error(`Unsupported operation type: ${operation.type}`);
    }
  }

  private applyReplace(content: string, operation: EditOperation): string {
    const { searchText, replaceText } = operation;
    return content.replace(new RegExp(searchText!, 'g'), replaceText!);
  }

  private applyInsert(lines: string[], operation: EditOperation): string {
    const { startLine, content } = operation;
    const insertIndex = Math.max(0, Math.min(startLine! - 1, lines.length));
    
    const contentLines = content!.split('\n');
    lines.splice(insertIndex, 0, ...contentLines);
    
    return lines.join('\n');
  }

  private applyDelete(lines: string[], operation: EditOperation): string {
    const { startLine, endLine } = operation;
    const start = Math.max(0, startLine! - 1);
    const end = endLine ? Math.min(endLine, lines.length) : start + 1;
    
    lines.splice(start, end - start);
    return lines.join('\n');
  }

  private generatePreview(original: string, modified: string): string {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    const maxLines = 10;
    const preview = [];
    
    preview.push('--- Original');
    preview.push('+++ Modified');
    
    const minLength = Math.min(originalLines.length, modifiedLines.length, maxLines);
    
    for (let i = 0; i < minLength; i++) {
      if (originalLines[i] !== modifiedLines[i]) {
        preview.push(`-${originalLines[i]}`);
        preview.push(`+${modifiedLines[i]}`);
      }
    }
    
    if (originalLines.length > maxLines || modifiedLines.length > maxLines) {
      preview.push('... (truncated)');
    }
    
    return preview.join('\n');
  }

  protected requiresApprovalForAutoEdit(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): boolean {
    // Auto-approve simple edits, require approval for complex operations
    const { operation } = parameters;
    return operation.type === 'replace' && operation.searchText.length > 100;
  }
}
