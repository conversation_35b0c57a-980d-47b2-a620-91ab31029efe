import { promises as fs } from 'fs';
import { resolve, relative } from 'path';
import { glob } from 'fast-glob';
import {
  Tool,
  ToolCategory,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';

interface GrepMatch {
  file: string;
  line: number;
  column: number;
  content: string;
  match: string;
  before?: string[];
  after?: string[];
}

export class GrepTool implements Tool {
  name = 'grep';
  description = 'Search for text patterns in files using regular expressions with context and filtering';

  parameters = {
    type: 'object' as const,
    properties: {
      pattern: {
        type: 'string',
        description: 'Regular expression pattern to search for',
      },
      files: {
        type: 'array',
        items: { type: 'string' },
        description: 'Specific files to search in',
      },
      glob: {
        type: 'string',
        description: 'Glob pattern to find files to search (e.g., "**/*.ts")',
      },
      ignoreCase: {
        type: 'boolean',
        description: 'Case-insensitive search (default: false)',
        default: false,
      },
      wholeWord: {
        type: 'boolean',
        description: 'Match whole words only (default: false)',
        default: false,
      },
      multiline: {
        type: 'boolean',
        description: 'Enable multiline mode for regex (default: false)',
        default: false,
      },
      contextBefore: {
        type: 'number',
        description: 'Number of lines to include before each match (default: 0)',
        default: 0,
      },
      contextAfter: {
        type: 'number',
        description: 'Number of lines to include after each match (default: 0)',
        default: 0,
      },
      maxMatches: {
        type: 'number',
        description: 'Maximum number of matches to return (default: 100)',
        default: 100,
      },
      maxFileSize: {
        type: 'number',
        description: 'Maximum file size to search in bytes (default: 10MB)',
        default: 10 * 1024 * 1024,
      },
      includeLineNumbers: {
        type: 'boolean',
        description: 'Include line numbers in results (default: true)',
        default: true,
      },
      includeFilenames: {
        type: 'boolean',
        description: 'Include filenames in results (default: true)',
        default: true,
      },
      encoding: {
        type: 'string',
        description: 'File encoding (default: utf8)',
        enum: ['utf8', 'ascii', 'latin1'],
        default: 'utf8',
      },
    },
    required: ['pattern'],
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const {
      pattern,
      files,
      glob: globPattern,
      ignoreCase = false,
      wholeWord = false,
      multiline = false,
      contextBefore = 0,
      contextAfter = 0,
      maxMatches = 100,
      maxFileSize = 10 * 1024 * 1024,
      includeLineNumbers = true,
      includeFilenames = true,
      encoding = 'utf8',
    } = parameters;

    try {
      // Validate pattern
      let regex: RegExp;
      try {
        let flags = '';
        if (ignoreCase) flags += 'i';
        if (multiline) flags += 'm';
        
        let searchPattern = pattern;
        if (wholeWord) {
          searchPattern = `\\b${pattern}\\b`;
        }
        
        regex = new RegExp(searchPattern, flags);
      } catch (error) {
        throw new ToolValidationError(
          `Invalid regular expression: ${error instanceof Error ? error.message : String(error)}`,
          'INVALID_REGEX',
          this.name
        );
      }

      // Determine files to search
      let filesToSearch: string[] = [];
      
      if (files && files.length > 0) {
        // Use specified files
        filesToSearch = files.map(f => resolve(context.workingDirectory, f));
      } else if (globPattern) {
        // Use glob pattern to find files
        const globResults = await glob(globPattern, {
          cwd: context.workingDirectory,
          onlyFiles: true,
          absolute: true,
          ignore: ['node_modules/**', '.git/**', 'dist/**', 'build/**'],
        });
        filesToSearch = globResults;
      } else {
        // Default: search all text files in current directory
        const defaultGlob = '**/*.{txt,md,js,ts,jsx,tsx,py,java,cpp,c,h,hpp,cs,php,rb,go,rs,swift,kt,html,css,scss,sass,less,xml,yaml,yml,json,toml,ini,cfg,sh,bash,zsh,fish,ps1,bat,sql,graphql,proto}';
        const globResults = await glob(defaultGlob, {
          cwd: context.workingDirectory,
          onlyFiles: true,
          absolute: true,
          ignore: ['node_modules/**', '.git/**', 'dist/**', 'build/**'],
        });
        filesToSearch = globResults;
      }

      // Search files
      const matches: GrepMatch[] = [];
      let filesSearched = 0;
      let filesSkipped = 0;

      for (const filePath of filesToSearch) {
        if (matches.length >= maxMatches) break;

        try {
          // Check file size
          const stats = await fs.stat(filePath);
          if (stats.size > maxFileSize) {
            filesSkipped++;
            continue;
          }

          // Read file content
          const content = await fs.readFile(filePath, encoding);
          const lines = content.split('\n');

          // Search for matches
          const fileMatches = this.searchInFile(
            filePath,
            lines,
            regex,
            contextBefore,
            contextAfter,
            context.workingDirectory
          );

          matches.push(...fileMatches);
          filesSearched++;

          // Limit total matches
          if (matches.length >= maxMatches) {
            matches.splice(maxMatches);
            break;
          }

        } catch (error) {
          // Skip files that can't be read
          filesSkipped++;
          continue;
        }
      }

      // Generate summary
      const summary = {
        pattern,
        totalMatches: matches.length,
        filesSearched,
        filesSkipped,
        truncated: matches.length >= maxMatches,
      };

      return {
        success: true,
        result: {
          matches: includeFilenames || includeLineNumbers ? matches : matches.map(m => m.content),
          summary,
          options: {
            ignoreCase,
            wholeWord,
            multiline,
            contextBefore,
            contextAfter,
            maxMatches,
            encoding,
          },
        },
        metadata: {
          operation: 'grep',
          pattern,
          matchCount: matches.length,
          filesSearched,
          filesSkipped,
        },
      };

    } catch (error) {
      return {
        success: false,
        error: `Search failed: ${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          operation: 'grep',
          pattern,
        },
      };
    }
  }

  private searchInFile(
    filePath: string,
    lines: string[],
    regex: RegExp,
    contextBefore: number,
    contextAfter: number,
    workingDirectory: string
  ): GrepMatch[] {
    const matches: GrepMatch[] = [];
    const relativePath = relative(workingDirectory, filePath);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const match = line.match(regex);

      if (match) {
        const grepMatch: GrepMatch = {
          file: relativePath,
          line: i + 1,
          column: match.index! + 1,
          content: line,
          match: match[0],
        };

        // Add context lines if requested
        if (contextBefore > 0) {
          grepMatch.before = [];
          for (let j = Math.max(0, i - contextBefore); j < i; j++) {
            grepMatch.before.push(lines[j]);
          }
        }

        if (contextAfter > 0) {
          grepMatch.after = [];
          for (let j = i + 1; j <= Math.min(lines.length - 1, i + contextAfter); j++) {
            grepMatch.after.push(lines[j]);
          }
        }

        matches.push(grepMatch);
      }
    }

    return matches;
  }

  requiresApproval(): boolean {
    return false; // Grep is read-only
  }

  validateParameters(parameters: Record<string, any>): boolean {
    return !!parameters.pattern;
  }

  getCategory(): ToolCategory {
    return ToolCategory.SEARCH;
  }

  isAvailable(): boolean {
    return true;
  }
}
