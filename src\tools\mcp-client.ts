import { WebSocket } from 'ws';
import { EventEmitter } from 'events';

export interface MCPMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface MCPPrompt {
  name: string;
  description?: string;
  arguments?: {
    name: string;
    description?: string;
    required?: boolean;
  }[];
}

export class MCPClient extends EventEmitter {
  private ws?: WebSocket;
  private url: string;
  private connected: boolean = false;
  private requestId: number = 1;
  private pendingRequests: Map<string | number, {
    resolve: (value: any) => void;
    reject: (error: any) => void;
    timeout: NodeJS.Timeout;
  }> = new Map();

  constructor(url: string) {
    super();
    this.url = url;
  }

  async connect(timeout: number = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, timeout);

      this.ws = new WebSocket(this.url);

      this.ws.on('open', () => {
        clearTimeout(timeoutId);
        this.connected = true;
        this.emit('connected');
        resolve();
      });

      this.ws.on('message', (data) => {
        try {
          const message: MCPMessage = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          this.emit('error', new Error(`Failed to parse message: ${error}`));
        }
      });

      this.ws.on('close', () => {
        this.connected = false;
        this.emit('disconnected');
        this.cleanup();
      });

      this.ws.on('error', (error) => {
        clearTimeout(timeoutId);
        this.emit('error', error);
        reject(error);
      });
    });
  }

  async disconnect(): Promise<void> {
    if (this.ws && this.connected) {
      this.ws.close();
      this.connected = false;
    }
  }

  async initialize(clientInfo: {
    name: string;
    version: string;
  }): Promise<any> {
    return this.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {},
        resources: {},
        prompts: {},
      },
      clientInfo,
    });
  }

  async listTools(): Promise<MCPTool[]> {
    const response = await this.sendRequest('tools/list');
    return response.tools || [];
  }

  async callTool(name: string, arguments_: any): Promise<any> {
    return this.sendRequest('tools/call', {
      name,
      arguments: arguments_,
    });
  }

  async listResources(): Promise<MCPResource[]> {
    const response = await this.sendRequest('resources/list');
    return response.resources || [];
  }

  async readResource(uri: string): Promise<any> {
    return this.sendRequest('resources/read', { uri });
  }

  async listPrompts(): Promise<MCPPrompt[]> {
    const response = await this.sendRequest('prompts/list');
    return response.prompts || [];
  }

  async getPrompt(name: string, arguments_?: any): Promise<any> {
    return this.sendRequest('prompts/get', {
      name,
      arguments: arguments_,
    });
  }

  async ping(): Promise<void> {
    await this.sendRequest('ping');
  }

  private async sendRequest(method: string, params?: any, timeout: number = 30000): Promise<any> {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    const id = this.requestId++;
    const message: MCPMessage = {
      jsonrpc: '2.0',
      id,
      method,
      params,
    };

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(id);
        reject(new Error(`Request timeout: ${method}`));
      }, timeout);

      this.pendingRequests.set(id, {
        resolve: (result) => {
          clearTimeout(timeoutId);
          resolve(result);
        },
        reject: (error) => {
          clearTimeout(timeoutId);
          reject(error);
        },
        timeout: timeoutId,
      });

      this.ws!.send(JSON.stringify(message));
    });
  }

  private sendNotification(method: string, params?: any): void {
    if (!this.connected) {
      return;
    }

    const message: MCPMessage = {
      jsonrpc: '2.0',
      method,
      params,
    };

    this.ws!.send(JSON.stringify(message));
  }

  private handleMessage(message: MCPMessage): void {
    if (message.id !== undefined) {
      // Response to a request
      const pending = this.pendingRequests.get(message.id);
      if (pending) {
        this.pendingRequests.delete(message.id);
        
        if (message.error) {
          pending.reject(new Error(`MCP Error ${message.error.code}: ${message.error.message}`));
        } else {
          pending.resolve(message.result);
        }
      }
    } else if (message.method) {
      // Notification from server
      this.emit('notification', message.method, message.params);
      
      // Handle specific notifications
      switch (message.method) {
        case 'notifications/tools/list_changed':
          this.emit('tools_changed');
          break;
        case 'notifications/resources/list_changed':
          this.emit('resources_changed');
          break;
        case 'notifications/prompts/list_changed':
          this.emit('prompts_changed');
          break;
      }
    }
  }

  private cleanup(): void {
    // Reject all pending requests
    for (const [id, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Connection closed'));
    }
    this.pendingRequests.clear();
  }

  isConnected(): boolean {
    return this.connected;
  }

  getUrl(): string {
    return this.url;
  }
}

export class MCPClientManager {
  private clients: Map<string, MCPClient> = new Map();

  async createClient(name: string, url: string): Promise<MCPClient> {
    if (this.clients.has(name)) {
      throw new Error(`MCP client '${name}' already exists`);
    }

    const client = new MCPClient(url);
    this.clients.set(name, client);
    return client;
  }

  getClient(name: string): MCPClient | undefined {
    return this.clients.get(name);
  }

  async connectClient(name: string, clientInfo?: { name: string; version: string }): Promise<void> {
    const client = this.clients.get(name);
    if (!client) {
      throw new Error(`MCP client '${name}' not found`);
    }

    await client.connect();
    
    if (clientInfo) {
      await client.initialize(clientInfo);
    }
  }

  async disconnectClient(name: string): Promise<void> {
    const client = this.clients.get(name);
    if (client) {
      await client.disconnect();
    }
  }

  async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.clients.values()).map(client => 
      client.disconnect().catch(() => {}) // Ignore errors during cleanup
    );
    await Promise.all(disconnectPromises);
  }

  removeClient(name: string): void {
    const client = this.clients.get(name);
    if (client) {
      client.disconnect().catch(() => {}); // Ignore errors
      this.clients.delete(name);
    }
  }

  listClients(): string[] {
    return Array.from(this.clients.keys());
  }

  getConnectedClients(): string[] {
    return Array.from(this.clients.entries())
      .filter(([_, client]) => client.isConnected())
      .map(([name, _]) => name);
  }
}
