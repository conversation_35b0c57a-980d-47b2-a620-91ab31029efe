import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';
import { MCPClient, MCPClientManager } from './mcp-client.js';

export class MCPTool implements Tool {
  name = 'mcp';
  description = 'Interact with Model Context Protocol (MCP) servers for extended functionality';

  private clientManager: MCPClientManager;

  constructor() {
    this.clientManager = new MCPClientManager();
  }

  parameters = {
    type: 'object' as const,
    properties: {
      operation: {
        type: 'string',
        description: 'MCP operation to perform',
        enum: [
          'connect',
          'disconnect',
          'list_clients',
          'list_tools',
          'call_tool',
          'list_resources',
          'read_resource',
          'list_prompts',
          'get_prompt',
          'ping',
        ],
      },
      clientName: {
        type: 'string',
        description: 'Name of the MCP client connection',
      },
      serverUrl: {
        type: 'string',
        description: 'WebSocket URL of the MCP server (for connect operation)',
      },
      toolName: {
        type: 'string',
        description: 'Name of the tool to call (for call_tool operation)',
      },
      toolArguments: {
        type: 'object',
        description: 'Arguments to pass to the tool (for call_tool operation)',
      },
      resourceUri: {
        type: 'string',
        description: 'URI of the resource to read (for read_resource operation)',
      },
      promptName: {
        type: 'string',
        description: 'Name of the prompt to get (for get_prompt operation)',
      },
      promptArguments: {
        type: 'object',
        description: 'Arguments to pass to the prompt (for get_prompt operation)',
      },
      timeout: {
        type: 'number',
        description: 'Operation timeout in milliseconds (default: 30000)',
        default: 30000,
      },
    },
    required: ['operation'],
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const {
      operation,
      clientName,
      serverUrl,
      toolName,
      toolArguments,
      resourceUri,
      promptName,
      promptArguments,
      timeout = 30000,
    } = parameters;

    try {
      switch (operation) {
        case 'connect':
          return await this.connectToServer(clientName, serverUrl, timeout);
        case 'disconnect':
          return await this.disconnectFromServer(clientName);
        case 'list_clients':
          return await this.listClients();
        case 'list_tools':
          return await this.listTools(clientName);
        case 'call_tool':
          return await this.callTool(clientName, toolName, toolArguments);
        case 'list_resources':
          return await this.listResources(clientName);
        case 'read_resource':
          return await this.readResource(clientName, resourceUri);
        case 'list_prompts':
          return await this.listPrompts(clientName);
        case 'get_prompt':
          return await this.getPrompt(clientName, promptName, promptArguments);
        case 'ping':
          return await this.pingServer(clientName);
        default:
          throw new ToolValidationError(
            `Invalid operation: ${operation}`,
            'INVALID_OPERATION',
            this.name
          );
      }
    } catch (error) {
      return {
        success: false,
        error: `MCP operation failed: ${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          operation: 'mcp',
          subOperation: operation,
          clientName,
        },
      };
    }
  }

  private async connectToServer(
    clientName: string,
    serverUrl: string,
    timeout: number
  ): Promise<ToolExecutionResult> {
    if (!clientName || !serverUrl) {
      throw new ToolValidationError(
        'Client name and server URL are required for connect operation',
        'MISSING_PARAMS',
        this.name
      );
    }

    try {
      const client = await this.clientManager.createClient(clientName, serverUrl);
      await this.clientManager.connectClient(clientName, {
        name: 'arien-ai-cli',
        version: '1.0.0',
      });

      return {
        success: true,
        result: {
          connected: true,
          clientName,
          serverUrl,
        },
        metadata: {
          operation: 'mcp_connect',
          clientName,
          serverUrl,
        },
      };
    } catch (error) {
      // Clean up failed client
      this.clientManager.removeClient(clientName);
      throw error;
    }
  }

  private async disconnectFromServer(clientName: string): Promise<ToolExecutionResult> {
    if (!clientName) {
      throw new ToolValidationError(
        'Client name is required for disconnect operation',
        'MISSING_CLIENT_NAME',
        this.name
      );
    }

    await this.clientManager.disconnectClient(clientName);
    this.clientManager.removeClient(clientName);

    return {
      success: true,
      result: {
        disconnected: true,
        clientName,
      },
      metadata: {
        operation: 'mcp_disconnect',
        clientName,
      },
    };
  }

  private async listClients(): Promise<ToolExecutionResult> {
    const allClients = this.clientManager.listClients();
    const connectedClients = this.clientManager.getConnectedClients();

    return {
      success: true,
      result: {
        clients: allClients.map(name => ({
          name,
          connected: connectedClients.includes(name),
          url: this.clientManager.getClient(name)?.getUrl(),
        })),
        totalClients: allClients.length,
        connectedClients: connectedClients.length,
      },
      metadata: {
        operation: 'mcp_list_clients',
        clientCount: allClients.length,
      },
    };
  }

  private async listTools(clientName: string): Promise<ToolExecutionResult> {
    const client = this.getClient(clientName);
    const tools = await client.listTools();

    return {
      success: true,
      result: {
        tools,
        count: tools.length,
        clientName,
      },
      metadata: {
        operation: 'mcp_list_tools',
        clientName,
        toolCount: tools.length,
      },
    };
  }

  private async callTool(
    clientName: string,
    toolName: string,
    toolArguments: any
  ): Promise<ToolExecutionResult> {
    if (!toolName) {
      throw new ToolValidationError(
        'Tool name is required for call_tool operation',
        'MISSING_TOOL_NAME',
        this.name
      );
    }

    const client = this.getClient(clientName);
    const result = await client.callTool(toolName, toolArguments || {});

    return {
      success: true,
      result: {
        toolName,
        arguments: toolArguments,
        result,
        clientName,
      },
      metadata: {
        operation: 'mcp_call_tool',
        clientName,
        toolName,
      },
    };
  }

  private async listResources(clientName: string): Promise<ToolExecutionResult> {
    const client = this.getClient(clientName);
    const resources = await client.listResources();

    return {
      success: true,
      result: {
        resources,
        count: resources.length,
        clientName,
      },
      metadata: {
        operation: 'mcp_list_resources',
        clientName,
        resourceCount: resources.length,
      },
    };
  }

  private async readResource(
    clientName: string,
    resourceUri: string
  ): Promise<ToolExecutionResult> {
    if (!resourceUri) {
      throw new ToolValidationError(
        'Resource URI is required for read_resource operation',
        'MISSING_RESOURCE_URI',
        this.name
      );
    }

    const client = this.getClient(clientName);
    const result = await client.readResource(resourceUri);

    return {
      success: true,
      result: {
        uri: resourceUri,
        content: result,
        clientName,
      },
      metadata: {
        operation: 'mcp_read_resource',
        clientName,
        resourceUri,
      },
    };
  }

  private async listPrompts(clientName: string): Promise<ToolExecutionResult> {
    const client = this.getClient(clientName);
    const prompts = await client.listPrompts();

    return {
      success: true,
      result: {
        prompts,
        count: prompts.length,
        clientName,
      },
      metadata: {
        operation: 'mcp_list_prompts',
        clientName,
        promptCount: prompts.length,
      },
    };
  }

  private async getPrompt(
    clientName: string,
    promptName: string,
    promptArguments: any
  ): Promise<ToolExecutionResult> {
    if (!promptName) {
      throw new ToolValidationError(
        'Prompt name is required for get_prompt operation',
        'MISSING_PROMPT_NAME',
        this.name
      );
    }

    const client = this.getClient(clientName);
    const result = await client.getPrompt(promptName, promptArguments || {});

    return {
      success: true,
      result: {
        promptName,
        arguments: promptArguments,
        prompt: result,
        clientName,
      },
      metadata: {
        operation: 'mcp_get_prompt',
        clientName,
        promptName,
      },
    };
  }

  private async pingServer(clientName: string): Promise<ToolExecutionResult> {
    const client = this.getClient(clientName);
    const startTime = Date.now();
    
    await client.ping();
    
    const duration = Date.now() - startTime;

    return {
      success: true,
      result: {
        ping: 'pong',
        duration,
        clientName,
      },
      metadata: {
        operation: 'mcp_ping',
        clientName,
        duration,
      },
    };
  }

  private getClient(clientName: string): MCPClient {
    if (!clientName) {
      throw new ToolValidationError(
        'Client name is required',
        'MISSING_CLIENT_NAME',
        this.name
      );
    }

    const client = this.clientManager.getClient(clientName);
    if (!client) {
      throw new ToolValidationError(
        `MCP client '${clientName}' not found`,
        'CLIENT_NOT_FOUND',
        this.name
      );
    }

    if (!client.isConnected()) {
      throw new ToolValidationError(
        `MCP client '${clientName}' is not connected`,
        'CLIENT_NOT_CONNECTED',
        this.name
      );
    }

    return client;
  }

  requiresApproval(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): boolean {
    const { operation } = parameters;
    
    // MCP operations require approval based on approval level
    switch (context.approvalLevel) {
      case 'yolo':
        return false;
      case 'auto-edit':
        // Auto-approve read-only operations
        return !['list_clients', 'list_tools', 'list_resources', 'list_prompts', 'ping'].includes(operation);
      case 'default':
      default:
        return true;
    }
  }

  validateParameters(parameters: Record<string, any>): boolean {
    return !!parameters.operation;
  }

  getCategory(): ToolCategory {
    return ToolCategory.MCP;
  }

  isAvailable(): boolean {
    return true;
  }

  // Cleanup method to disconnect all clients
  async cleanup(): Promise<void> {
    await this.clientManager.disconnectAll();
  }
}
