import { promises as fs } from 'fs';
import { resolve, dirname } from 'path';
import Conf from 'conf';
import {
  Tool,
  ToolCategory,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';

interface MemoryEntry {
  id: string;
  key: string;
  value: any;
  tags: string[];
  timestamp: Date;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export class MemoryTool implements Tool {
  name = 'memory';
  description = 'Store, retrieve, and manage contextual memory for conversations and sessions';

  private memoryStore: Conf<any>;

  constructor() {
    this.memoryStore = new Conf({
      projectName: 'arien-ai',
      configName: 'memory',
      defaults: {
        entries: {},
        sessions: {},
        globalContext: {},
      },
    });
  }

  parameters = {
    type: 'object' as const,
    properties: {
      operation: {
        type: 'string',
        description: 'Memory operation to perform',
        enum: ['store', 'retrieve', 'delete', 'search', 'list', 'clear', 'export', 'import'],
      },
      key: {
        type: 'string',
        description: 'Memory key for store/retrieve/delete operations',
      },
      value: {
        type: 'string',
        description: 'Value to store (for store operation)',
      },
      tags: {
        type: 'array',
        items: { type: 'string' },
        description: 'Tags to associate with the memory entry',
        default: [],
      },
      query: {
        type: 'string',
        description: 'Search query for search operation',
      },
      sessionId: {
        type: 'string',
        description: 'Session ID to scope memory operations',
      },
      global: {
        type: 'boolean',
        description: 'Whether to use global memory scope (default: false)',
        default: false,
      },
      limit: {
        type: 'number',
        description: 'Maximum number of results to return (default: 50)',
        default: 50,
      },
      includeMetadata: {
        type: 'boolean',
        description: 'Include metadata in results (default: false)',
        default: false,
      },
      exportPath: {
        type: 'string',
        description: 'File path for export operation',
      },
      importPath: {
        type: 'string',
        description: 'File path for import operation',
      },
    },
    required: ['operation'],
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const {
      operation,
      key,
      value,
      tags = [],
      query,
      sessionId,
      global = false,
      limit = 50,
      includeMetadata = false,
      exportPath,
      importPath,
    } = parameters;

    try {
      switch (operation) {
        case 'store':
          return await this.storeMemory(key, value, tags, sessionId, global, context);
        case 'retrieve':
          return await this.retrieveMemory(key, sessionId, global, includeMetadata);
        case 'delete':
          return await this.deleteMemory(key, sessionId, global);
        case 'search':
          return await this.searchMemory(query, sessionId, global, limit, includeMetadata);
        case 'list':
          return await this.listMemory(sessionId, global, limit, includeMetadata);
        case 'clear':
          return await this.clearMemory(sessionId, global);
        case 'export':
          return await this.exportMemory(exportPath, sessionId, global, context);
        case 'import':
          return await this.importMemory(importPath, sessionId, global, context);
        default:
          throw new ToolValidationError(
            `Invalid operation: ${operation}`,
            'INVALID_OPERATION',
            this.name
          );
      }
    } catch (error) {
      return {
        success: false,
        error: `Memory operation failed: ${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          operation: 'memory',
          subOperation: operation,
        },
      };
    }
  }

  private async storeMemory(
    key: string,
    value: any,
    tags: string[],
    sessionId?: string,
    global?: boolean,
    context?: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    if (!key) {
      throw new ToolValidationError('Key is required for store operation', 'MISSING_KEY', this.name);
    }

    const entryId = this.generateId();
    const entry: MemoryEntry = {
      id: entryId,
      key,
      value,
      tags: Array.isArray(tags) ? tags : [],
      timestamp: new Date(),
      sessionId: global ? undefined : sessionId,
      metadata: {
        workingDirectory: context?.workingDirectory,
      },
    };

    const scope = this.getScope(sessionId, global);
    const entries = this.memoryStore.get(`entries.${scope}`, {});
    entries[key] = entry;
    this.memoryStore.set(`entries.${scope}`, entries);

    return {
      success: true,
      result: {
        stored: true,
        key,
        entryId,
        scope,
        tags: entry.tags,
        timestamp: entry.timestamp,
      },
      metadata: {
        operation: 'memory_store',
        key,
        scope,
      },
    };
  }

  private async retrieveMemory(
    key: string,
    sessionId?: string,
    global?: boolean,
    includeMetadata?: boolean
  ): Promise<ToolExecutionResult> {
    if (!key) {
      throw new ToolValidationError('Key is required for retrieve operation', 'MISSING_KEY', this.name);
    }

    const scope = this.getScope(sessionId, global);
    const entries = this.memoryStore.get(`entries.${scope}`, {});
    const entry = entries[key];

    if (!entry) {
      return {
        success: false,
        error: `Memory entry not found: ${key}`,
        metadata: {
          operation: 'memory_retrieve',
          key,
          scope,
        },
      };
    }

    const result: any = {
      key: entry.key,
      value: entry.value,
      tags: entry.tags,
      timestamp: entry.timestamp,
    };

    if (includeMetadata) {
      result.metadata = entry.metadata;
      result.id = entry.id;
      result.sessionId = entry.sessionId;
    }

    return {
      success: true,
      result,
      metadata: {
        operation: 'memory_retrieve',
        key,
        scope,
      },
    };
  }

  private async deleteMemory(
    key: string,
    sessionId?: string,
    global?: boolean
  ): Promise<ToolExecutionResult> {
    if (!key) {
      throw new ToolValidationError('Key is required for delete operation', 'MISSING_KEY', this.name);
    }

    const scope = this.getScope(sessionId, global);
    const entries = this.memoryStore.get(`entries.${scope}`, {});
    
    if (!entries[key]) {
      return {
        success: false,
        error: `Memory entry not found: ${key}`,
        metadata: {
          operation: 'memory_delete',
          key,
          scope,
        },
      };
    }

    delete entries[key];
    this.memoryStore.set(`entries.${scope}`, entries);

    return {
      success: true,
      result: {
        deleted: true,
        key,
        scope,
      },
      metadata: {
        operation: 'memory_delete',
        key,
        scope,
      },
    };
  }

  private async searchMemory(
    query: string,
    sessionId?: string,
    global?: boolean,
    limit?: number,
    includeMetadata?: boolean
  ): Promise<ToolExecutionResult> {
    if (!query) {
      throw new ToolValidationError('Query is required for search operation', 'MISSING_QUERY', this.name);
    }

    const scope = this.getScope(sessionId, global);
    const entries = this.memoryStore.get(`entries.${scope}`, {});
    const results: any[] = [];

    const searchRegex = new RegExp(query, 'i');

    for (const [key, entry] of Object.entries(entries) as [string, MemoryEntry][]) {
      const searchText = `${entry.key} ${entry.value} ${entry.tags.join(' ')}`;
      if (searchRegex.test(searchText)) {
        const result: any = {
          key: entry.key,
          value: entry.value,
          tags: entry.tags,
          timestamp: entry.timestamp,
        };

        if (includeMetadata) {
          result.metadata = entry.metadata;
          result.id = entry.id;
          result.sessionId = entry.sessionId;
        }

        results.push(result);
      }
    }

    // Sort by timestamp (newest first)
    results.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return {
      success: true,
      result: {
        query,
        matches: results.slice(0, limit),
        totalMatches: results.length,
        scope,
      },
      metadata: {
        operation: 'memory_search',
        query,
        matchCount: results.length,
        scope,
      },
    };
  }

  private async listMemory(
    sessionId?: string,
    global?: boolean,
    limit?: number,
    includeMetadata?: boolean
  ): Promise<ToolExecutionResult> {
    const scope = this.getScope(sessionId, global);
    const entries = this.memoryStore.get(`entries.${scope}`, {});
    const results: any[] = [];

    for (const [key, entry] of Object.entries(entries) as [string, MemoryEntry][]) {
      const result: any = {
        key: entry.key,
        value: entry.value,
        tags: entry.tags,
        timestamp: entry.timestamp,
      };

      if (includeMetadata) {
        result.metadata = entry.metadata;
        result.id = entry.id;
        result.sessionId = entry.sessionId;
      }

      results.push(result);
    }

    // Sort by timestamp (newest first)
    results.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return {
      success: true,
      result: {
        entries: results.slice(0, limit),
        totalEntries: results.length,
        scope,
      },
      metadata: {
        operation: 'memory_list',
        entryCount: results.length,
        scope,
      },
    };
  }

  private async clearMemory(
    sessionId?: string,
    global?: boolean
  ): Promise<ToolExecutionResult> {
    const scope = this.getScope(sessionId, global);
    const entries = this.memoryStore.get(`entries.${scope}`, {});
    const entryCount = Object.keys(entries).length;

    this.memoryStore.set(`entries.${scope}`, {});

    return {
      success: true,
      result: {
        cleared: true,
        entriesRemoved: entryCount,
        scope,
      },
      metadata: {
        operation: 'memory_clear',
        entriesRemoved: entryCount,
        scope,
      },
    };
  }

  private async exportMemory(
    exportPath: string,
    sessionId?: string,
    global?: boolean,
    context?: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    if (!exportPath) {
      throw new ToolValidationError('Export path is required', 'MISSING_EXPORT_PATH', this.name);
    }

    const scope = this.getScope(sessionId, global);
    const entries = this.memoryStore.get(`entries.${scope}`, {});
    
    const fullPath = resolve(context?.workingDirectory || process.cwd(), exportPath);
    await fs.mkdir(dirname(fullPath), { recursive: true });
    
    const exportData = {
      scope,
      exportedAt: new Date().toISOString(),
      entries,
    };

    await fs.writeFile(fullPath, JSON.stringify(exportData, null, 2), 'utf8');

    return {
      success: true,
      result: {
        exported: true,
        path: fullPath,
        entryCount: Object.keys(entries).length,
        scope,
      },
      metadata: {
        operation: 'memory_export',
        path: fullPath,
        entryCount: Object.keys(entries).length,
        scope,
      },
    };
  }

  private async importMemory(
    importPath: string,
    sessionId?: string,
    global?: boolean,
    context?: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    if (!importPath) {
      throw new ToolValidationError('Import path is required', 'MISSING_IMPORT_PATH', this.name);
    }

    const fullPath = resolve(context?.workingDirectory || process.cwd(), importPath);
    const importData = JSON.parse(await fs.readFile(fullPath, 'utf8'));

    if (!importData.entries) {
      throw new ToolValidationError('Invalid import file format', 'INVALID_IMPORT_FORMAT', this.name);
    }

    const scope = this.getScope(sessionId, global);
    const existingEntries = this.memoryStore.get(`entries.${scope}`, {});
    const mergedEntries = { ...existingEntries, ...importData.entries };
    
    this.memoryStore.set(`entries.${scope}`, mergedEntries);

    return {
      success: true,
      result: {
        imported: true,
        path: fullPath,
        entriesImported: Object.keys(importData.entries).length,
        scope,
      },
      metadata: {
        operation: 'memory_import',
        path: fullPath,
        entriesImported: Object.keys(importData.entries).length,
        scope,
      },
    };
  }

  private getScope(sessionId?: string, global?: boolean): string {
    if (global) {
      return 'global';
    }
    return sessionId || 'default';
  }

  private generateId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  requiresApproval(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): boolean {
    const { operation } = parameters;
    
    // Only require approval for destructive operations
    switch (context.approvalLevel) {
      case 'yolo':
        return false;
      case 'auto-edit':
        return ['clear', 'delete'].includes(operation);
      case 'default':
      default:
        return ['clear', 'delete', 'import'].includes(operation);
    }
  }

  validateParameters(parameters: Record<string, any>): boolean {
    return !!parameters.operation;
  }

  getCategory(): ToolCategory {
    return ToolCategory.MEMORY;
  }

  isAvailable(): boolean {
    return true;
  }
}
