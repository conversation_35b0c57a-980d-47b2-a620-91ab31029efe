import { promises as fs } from 'fs';
import { dirname, resolve, basename } from 'path';
import {
  Tool,
  Tool<PERSON>ategor<PERSON>,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';

/**
 * Base class for tools that modify files or the file system.
 * Provides common functionality for backup, validation, and safety checks.
 */
export abstract class ModifiableTool implements Tool {
  abstract name: string;
  abstract description: string;
  abstract parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };

  abstract execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult>;

  getCategory(): ToolCategory {
    return ToolCategory.FILE_SYSTEM;
  }

  isAvailable(context: ToolExecutionContext): boolean {
    return true;
  }

  validateParameters(parameters: Record<string, any>): boolean {
    const required = this.parameters.required || [];
    return required.every(param => param in parameters);
  }

  requiresApproval(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): boolean {
    // Modifiable tools generally require approval unless in YOLO mode
    switch (context.approvalLevel) {
      case 'yolo':
        return false;
      case 'auto-edit':
        return this.requiresApprovalForAutoEdit(parameters, context);
      case 'default':
      default:
        return true;
    }
  }

  /**
   * Override this method to customize approval requirements for auto-edit mode
   */
  protected requiresApprovalForAutoEdit(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): boolean {
    return false; // Default: auto-approve in auto-edit mode
  }

  /**
   * Validates that a path is within the working directory
   */
  protected validatePath(path: string, context: ToolExecutionContext): string {
    const fullPath = resolve(context.workingDirectory, path);
    
    // Security check: ensure path is within working directory
    if (!fullPath.startsWith(resolve(context.workingDirectory))) {
      throw new ToolValidationError(
        'Path is outside working directory',
        'INVALID_PATH',
        this.name,
        { path, workingDirectory: context.workingDirectory }
      );
    }

    return fullPath;
  }

  /**
   * Creates a backup of a file before modification
   */
  protected async createBackup(filePath: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${filePath}.backup-${timestamp}`;
      
      await fs.copyFile(filePath, backupPath);
      return backupPath;
    } catch (error) {
      throw new Error(`Failed to create backup: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Ensures a directory exists, creating it if necessary
   */
  protected async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      throw new Error(`Failed to create directory: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Checks if a file exists
   */
  protected async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Gets file stats safely
   */
  protected async getFileStats(filePath: string) {
    try {
      return await fs.stat(filePath);
    } catch (error) {
      throw new Error(`Failed to get file stats: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validates file size against limits
   */
  protected validateFileSize(size: number, maxSize: number = 50 * 1024 * 1024): void {
    if (size > maxSize) {
      throw new ToolValidationError(
        `File size ${size} bytes exceeds maximum allowed size ${maxSize} bytes`,
        'FILE_TOO_LARGE',
        this.name,
        { size, maxSize }
      );
    }
  }

  /**
   * Checks if a file extension is allowed for modification
   */
  protected isAllowedExtension(filePath: string, allowedExtensions?: string[]): boolean {
    if (!allowedExtensions) {
      // Default allowed extensions for text files
      allowedExtensions = [
        '.txt', '.md', '.json', '.js', '.ts', '.jsx', '.tsx',
        '.py', '.java', '.cpp', '.c', '.h', '.hpp', '.cs',
        '.php', '.rb', '.go', '.rs', '.swift', '.kt',
        '.html', '.css', '.scss', '.sass', '.less',
        '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg',
        '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat',
        '.sql', '.graphql', '.proto', '.dockerfile',
        '.gitignore', '.gitattributes', '.editorconfig',
      ];
    }

    const ext = basename(filePath).toLowerCase();
    return allowedExtensions.some(allowed => ext.endsWith(allowed));
  }

  /**
   * Sanitizes file content to prevent issues
   */
  protected sanitizeContent(content: string): string {
    // Remove null bytes and other problematic characters
    return content.replace(/\0/g, '').replace(/\r\n/g, '\n');
  }

  /**
   * Creates a success result with metadata
   */
  protected createSuccessResult(
    result: any,
    operation: string,
    metadata: Record<string, any> = {}
  ): ToolExecutionResult {
    return {
      success: true,
      result,
      metadata: {
        operation,
        timestamp: new Date().toISOString(),
        tool: this.name,
        ...metadata,
      },
    };
  }

  /**
   * Creates an error result
   */
  protected createErrorResult(
    error: string,
    metadata: Record<string, any> = {}
  ): ToolExecutionResult {
    return {
      success: false,
      error,
      metadata: {
        timestamp: new Date().toISOString(),
        tool: this.name,
        ...metadata,
      },
    };
  }

  /**
   * Handles common file operation errors
   */
  protected handleFileError(error: any, operation: string, filePath?: string): ToolExecutionResult {
    let errorMessage = `${operation} failed`;
    
    if (error instanceof Error) {
      errorMessage += `: ${error.message}`;
    } else {
      errorMessage += `: ${String(error)}`;
    }

    const metadata: Record<string, any> = { operation };
    if (filePath) {
      metadata.filePath = filePath;
    }

    return this.createErrorResult(errorMessage, metadata);
  }
}
