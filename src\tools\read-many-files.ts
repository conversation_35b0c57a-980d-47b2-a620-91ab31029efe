import { promises as fs } from 'fs';
import { resolve, relative } from 'path';
import { glob } from 'fast-glob';
import {
  Tool,
  ToolCategory,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';

interface FileContent {
  path: string;
  relativePath: string;
  content: string;
  size: number;
  encoding: string;
  error?: string;
}

export class ReadManyFilesTool implements Tool {
  name = 'read_many_files';
  description = 'Read multiple files at once with glob patterns, filtering, and batch processing';

  parameters = {
    type: 'object' as const,
    properties: {
      paths: {
        type: 'array',
        items: { type: 'string' },
        description: 'Array of file paths to read',
      },
      glob: {
        type: 'string',
        description: 'Glob pattern to find files to read (e.g., "src/**/*.ts")',
      },
      encoding: {
        type: 'string',
        description: 'File encoding (default: utf8)',
        enum: ['utf8', 'ascii', 'base64', 'hex', 'latin1'],
        default: 'utf8',
      },
      maxFileSize: {
        type: 'number',
        description: 'Maximum file size to read in bytes (default: 1MB)',
        default: 1024 * 1024,
      },
      maxTotalSize: {
        type: 'number',
        description: 'Maximum total size of all files in bytes (default: 10MB)',
        default: 10 * 1024 * 1024,
      },
      maxFiles: {
        type: 'number',
        description: 'Maximum number of files to read (default: 50)',
        default: 50,
      },
      includeEmpty: {
        type: 'boolean',
        description: 'Include empty files in results (default: true)',
        default: true,
      },
      includeBinary: {
        type: 'boolean',
        description: 'Include binary files (default: false)',
        default: false,
      },
      skipErrors: {
        type: 'boolean',
        description: 'Skip files that cannot be read instead of failing (default: true)',
        default: true,
      },
      sortBy: {
        type: 'string',
        description: 'Sort files by: name, size, modified (default: name)',
        enum: ['name', 'size', 'modified'],
        default: 'name',
      },
      sortOrder: {
        type: 'string',
        description: 'Sort order: asc, desc (default: asc)',
        enum: ['asc', 'desc'],
        default: 'asc',
      },
      includeStats: {
        type: 'boolean',
        description: 'Include file statistics (default: false)',
        default: false,
      },
    },
    required: [],
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const {
      paths,
      glob: globPattern,
      encoding = 'utf8',
      maxFileSize = 1024 * 1024,
      maxTotalSize = 10 * 1024 * 1024,
      maxFiles = 50,
      includeEmpty = true,
      includeBinary = false,
      skipErrors = true,
      sortBy = 'name',
      sortOrder = 'asc',
      includeStats = false,
    } = parameters;

    try {
      // Validate input
      if (!paths && !globPattern) {
        throw new ToolValidationError(
          'Either paths or glob pattern must be provided',
          'MISSING_INPUT',
          this.name
        );
      }

      // Determine files to read
      let filesToRead: string[] = [];

      if (paths && paths.length > 0) {
        // Use specified paths
        filesToRead = paths.map((p: string) => resolve(context.workingDirectory, p));
      } else if (globPattern) {
        // Use glob pattern
        const globResults = await glob(globPattern, {
          cwd: context.workingDirectory,
          onlyFiles: true,
          absolute: true,
          ignore: ['node_modules/**', '.git/**', 'dist/**', 'build/**'],
        });
        filesToRead = globResults;
      }

      // Sort files if requested
      if (sortBy !== 'name') {
        filesToRead = await this.sortFiles(filesToRead, sortBy, sortOrder);
      } else {
        filesToRead.sort((a, b) => {
          const comparison = a.localeCompare(b);
          return sortOrder === 'desc' ? -comparison : comparison;
        });
      }

      // Limit number of files
      if (filesToRead.length > maxFiles) {
        filesToRead = filesToRead.slice(0, maxFiles);
      }

      // Read files
      const results: FileContent[] = [];
      let totalSize = 0;
      let filesRead = 0;
      let filesSkipped = 0;
      let errors: string[] = [];

      for (const filePath of filesToRead) {
        try {
          // Check if we've exceeded total size limit
          if (totalSize >= maxTotalSize) {
            break;
          }

          // Get file stats
          const stats = await fs.stat(filePath);

          // Skip if file is too large
          if (stats.size > maxFileSize) {
            filesSkipped++;
            if (!skipErrors) {
              errors.push(`File too large: ${relative(context.workingDirectory, filePath)} (${stats.size} bytes)`);
            }
            continue;
          }

          // Skip empty files if not included
          if (!includeEmpty && stats.size === 0) {
            filesSkipped++;
            continue;
          }

          // Check if binary file
          if (!includeBinary && await this.isBinaryFile(filePath)) {
            filesSkipped++;
            continue;
          }

          // Read file content
          const content = await fs.readFile(filePath, encoding);
          
          const fileContent: FileContent = {
            path: filePath,
            relativePath: relative(context.workingDirectory, filePath),
            content,
            size: stats.size,
            encoding,
          };

          if (includeStats) {
            (fileContent as any).stats = {
              size: stats.size,
              modified: stats.mtime,
              created: stats.birthtime,
              isFile: stats.isFile(),
              isDirectory: stats.isDirectory(),
              mode: stats.mode,
            };
          }

          results.push(fileContent);
          totalSize += stats.size;
          filesRead++;

        } catch (error) {
          filesSkipped++;
          const errorMsg = `Failed to read ${relative(context.workingDirectory, filePath)}: ${error instanceof Error ? error.message : String(error)}`;
          
          if (skipErrors) {
            errors.push(errorMsg);
          } else {
            throw new Error(errorMsg);
          }
        }
      }

      // Calculate summary statistics
      const summary = {
        totalFiles: filesToRead.length,
        filesRead,
        filesSkipped,
        totalSize,
        averageSize: filesRead > 0 ? Math.round(totalSize / filesRead) : 0,
        errors: errors.length,
        truncated: filesToRead.length > maxFiles || totalSize >= maxTotalSize,
      };

      return {
        success: true,
        result: {
          files: results,
          summary,
          errors: errors.length > 0 ? errors : undefined,
          options: {
            encoding,
            maxFileSize,
            maxTotalSize,
            maxFiles,
            includeEmpty,
            includeBinary,
            skipErrors,
            sortBy,
            sortOrder,
          },
        },
        metadata: {
          operation: 'read_many_files',
          filesRead,
          filesSkipped,
          totalSize,
          workingDirectory: context.workingDirectory,
        },
      };

    } catch (error) {
      return {
        success: false,
        error: `Batch file read failed: ${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          operation: 'read_many_files',
        },
      };
    }
  }

  private async sortFiles(files: string[], sortBy: string, sortOrder: string): Promise<string[]> {
    const filesWithStats = await Promise.all(
      files.map(async (file) => {
        try {
          const stats = await fs.stat(file);
          return { file, stats };
        } catch {
          return { file, stats: null };
        }
      })
    );

    filesWithStats.sort((a, b) => {
      if (!a.stats || !b.stats) return 0;

      let comparison = 0;
      switch (sortBy) {
        case 'size':
          comparison = a.stats.size - b.stats.size;
          break;
        case 'modified':
          comparison = a.stats.mtime.getTime() - b.stats.mtime.getTime();
          break;
        default:
          comparison = a.file.localeCompare(b.file);
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filesWithStats.map(item => item.file);
  }

  private async isBinaryFile(filePath: string): Promise<boolean> {
    try {
      // Read first 1024 bytes to check for binary content
      const buffer = Buffer.alloc(1024);
      const fd = await fs.open(filePath, 'r');
      const { bytesRead } = await fd.read(buffer, 0, 1024, 0);
      await fd.close();

      // Check for null bytes (common in binary files)
      for (let i = 0; i < bytesRead; i++) {
        if (buffer[i] === 0) {
          return true;
        }
      }

      return false;
    } catch {
      return false;
    }
  }

  requiresApproval(): boolean {
    return false; // Reading files is generally safe
  }

  validateParameters(parameters: Record<string, any>): boolean {
    const { paths, glob } = parameters;
    return !!(paths || glob);
  }

  getCategory(): ToolCategory {
    return ToolCategory.FILE_SYSTEM;
  }

  isAvailable(): boolean {
    return true;
  }
}
