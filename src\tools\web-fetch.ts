import fetch from 'node-fetch';
import {
  <PERSON><PERSON>,
  <PERSON>lCategory,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';

export class WebFetchTool implements Tool {
  name = 'web_fetch';
  description = 'Fetch content from web URLs with support for various content types and request options';

  parameters = {
    type: 'object' as const,
    properties: {
      url: {
        type: 'string',
        description: 'URL to fetch content from',
      },
      method: {
        type: 'string',
        description: 'HTTP method (default: GET)',
        enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD'],
        default: 'GET',
      },
      headers: {
        type: 'object',
        description: 'HTTP headers to include in the request',
        additionalProperties: { type: 'string' },
      },
      body: {
        type: 'string',
        description: 'Request body for POST/PUT requests',
      },
      timeout: {
        type: 'number',
        description: 'Request timeout in milliseconds (default: 30000)',
        default: 30000,
      },
      followRedirects: {
        type: 'boolean',
        description: 'Follow HTTP redirects (default: true)',
        default: true,
      },
      maxRedirects: {
        type: 'number',
        description: 'Maximum number of redirects to follow (default: 5)',
        default: 5,
      },
      userAgent: {
        type: 'string',
        description: 'User-Agent header (default: arien-ai-cli)',
        default: 'arien-ai-cli/1.0.0',
      },
      responseType: {
        type: 'string',
        description: 'Expected response type (default: auto)',
        enum: ['text', 'json', 'buffer', 'auto'],
        default: 'auto',
      },
      maxSize: {
        type: 'number',
        description: 'Maximum response size in bytes (default: 10MB)',
        default: 10 * 1024 * 1024,
      },
      includeHeaders: {
        type: 'boolean',
        description: 'Include response headers in result (default: false)',
        default: false,
      },
      validateSSL: {
        type: 'boolean',
        description: 'Validate SSL certificates (default: true)',
        default: true,
      },
    },
    required: ['url'],
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const {
      url,
      method = 'GET',
      headers = {},
      body,
      timeout = 30000,
      followRedirects = true,
      maxRedirects = 5,
      userAgent = 'arien-ai-cli/1.0.0',
      responseType = 'auto',
      maxSize = 10 * 1024 * 1024,
      includeHeaders = false,
      validateSSL = true,
    } = parameters;

    try {
      // Validate URL
      let parsedUrl: URL;
      try {
        parsedUrl = new URL(url);
      } catch (error) {
        throw new ToolValidationError(
          `Invalid URL: ${url}`,
          'INVALID_URL',
          this.name
        );
      }

      // Security check: only allow HTTP/HTTPS
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        throw new ToolValidationError(
          `Unsupported protocol: ${parsedUrl.protocol}`,
          'UNSUPPORTED_PROTOCOL',
          this.name
        );
      }

      // Prepare request options
      const requestOptions: any = {
        method: method.toUpperCase(),
        headers: {
          'User-Agent': userAgent,
          ...headers,
        },
        timeout,
        redirect: followRedirects ? 'follow' : 'manual',
        follow: maxRedirects,
        size: maxSize,
      };

      // Add body for POST/PUT requests
      if (body && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        requestOptions.body = body;
        
        // Set content-type if not provided
        if (!requestOptions.headers['Content-Type'] && !requestOptions.headers['content-type']) {
          requestOptions.headers['Content-Type'] = 'application/json';
        }
      }

      // Handle SSL validation
      if (!validateSSL) {
        process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
      }

      // Make the request
      const startTime = Date.now();
      const response = await fetch(url, requestOptions);
      const duration = Date.now() - startTime;

      // Reset SSL validation
      if (!validateSSL) {
        delete process.env.NODE_TLS_REJECT_UNAUTHORIZED;
      }

      // Check response status
      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
          metadata: {
            operation: 'web_fetch',
            url,
            method,
            status: response.status,
            statusText: response.statusText,
            duration,
          },
        };
      }

      // Get content type
      const contentType = response.headers.get('content-type') || '';
      
      // Determine how to parse response
      let content: any;
      let actualResponseType = responseType;

      if (responseType === 'auto') {
        if (contentType.includes('application/json')) {
          actualResponseType = 'json';
        } else if (contentType.includes('text/') || contentType.includes('application/xml')) {
          actualResponseType = 'text';
        } else {
          actualResponseType = 'buffer';
        }
      }

      // Parse response based on type
      switch (actualResponseType) {
        case 'json':
          try {
            content = await response.json();
          } catch (error) {
            content = await response.text();
            actualResponseType = 'text';
          }
          break;
        case 'text':
          content = await response.text();
          break;
        case 'buffer':
          const buffer = await response.buffer();
          content = {
            type: 'buffer',
            size: buffer.length,
            data: buffer.toString('base64'),
          };
          break;
        default:
          content = await response.text();
          actualResponseType = 'text';
      }

      // Prepare result
      const result: any = {
        url,
        status: response.status,
        statusText: response.statusText,
        contentType,
        responseType: actualResponseType,
        content,
        size: typeof content === 'string' ? content.length : JSON.stringify(content).length,
        duration,
      };

      // Include headers if requested
      if (includeHeaders) {
        result.headers = Object.fromEntries(response.headers.entries());
      }

      // Add metadata about the request
      const metadata = {
        operation: 'web_fetch',
        url,
        method,
        status: response.status,
        contentType,
        responseType: actualResponseType,
        duration,
        size: result.size,
      };

      return {
        success: true,
        result,
        metadata,
      };

    } catch (error) {
      // Reset SSL validation in case of error
      if (!validateSSL) {
        delete process.env.NODE_TLS_REJECT_UNAUTHORIZED;
      }

      let errorMessage = 'Web fetch failed';
      let errorCode = 'FETCH_ERROR';

      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
        
        // Categorize common errors
        if (error.message.includes('timeout')) {
          errorCode = 'TIMEOUT';
        } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
          errorCode = 'CONNECTION_ERROR';
        } else if (error.message.includes('certificate')) {
          errorCode = 'SSL_ERROR';
        }
      }

      return {
        success: false,
        error: errorMessage,
        metadata: {
          operation: 'web_fetch',
          url,
          method,
          errorCode,
        },
      };
    }
  }

  requiresApproval(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): boolean {
    // Web requests require approval based on approval level
    switch (context.approvalLevel) {
      case 'yolo':
        return false;
      case 'auto-edit':
        // Auto-approve GET requests to common domains
        const { method = 'GET', url } = parameters;
        if (method.toUpperCase() === 'GET') {
          try {
            const parsedUrl = new URL(url);
            const trustedDomains = [
              'github.com', 'api.github.com',
              'stackoverflow.com', 'stackexchange.com',
              'npmjs.com', 'registry.npmjs.org',
              'pypi.org', 'crates.io',
              'docs.rs', 'doc.rust-lang.org',
              'developer.mozilla.org',
            ];
            return !trustedDomains.some(domain => parsedUrl.hostname.includes(domain));
          } catch {
            return true;
          }
        }
        return true;
      case 'default':
      default:
        return true;
    }
  }

  validateParameters(parameters: Record<string, any>): boolean {
    return !!parameters.url;
  }

  getCategory(): ToolCategory {
    return ToolCategory.WEB;
  }

  isAvailable(): boolean {
    return true;
  }
}
