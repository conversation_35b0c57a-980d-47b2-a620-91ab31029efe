import fetch from 'node-fetch';
import {
  Tool,
  ToolCategory,
  ToolExecutionContext,
  ToolExecutionResult,
  ToolValidationError,
} from '../types/tools.js';

interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  displayUrl?: string;
  favicon?: string;
}

export class WebSearchTool implements Tool {
  name = 'web_search';
  description = 'Search the web using various search engines and return formatted results';

  parameters = {
    type: 'object' as const,
    properties: {
      query: {
        type: 'string',
        description: 'Search query to execute',
      },
      engine: {
        type: 'string',
        description: 'Search engine to use (default: duckduckgo)',
        enum: ['duckduckgo', 'searx', 'custom'],
        default: 'duckduckgo',
      },
      maxResults: {
        type: 'number',
        description: 'Maximum number of results to return (default: 10)',
        default: 10,
        minimum: 1,
        maximum: 50,
      },
      safeSearch: {
        type: 'boolean',
        description: 'Enable safe search filtering (default: true)',
        default: true,
      },
      region: {
        type: 'string',
        description: 'Search region/country code (e.g., "us", "uk", "de")',
        default: 'us',
      },
      language: {
        type: 'string',
        description: 'Search language code (e.g., "en", "es", "fr")',
        default: 'en',
      },
      timeout: {
        type: 'number',
        description: 'Request timeout in milliseconds (default: 15000)',
        default: 15000,
      },
      includeSnippets: {
        type: 'boolean',
        description: 'Include result snippets (default: true)',
        default: true,
      },
      customEndpoint: {
        type: 'string',
        description: 'Custom search API endpoint (for engine: custom)',
      },
      apiKey: {
        type: 'string',
        description: 'API key for custom search endpoint',
      },
    },
    required: ['query'],
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const {
      query,
      engine = 'duckduckgo',
      maxResults = 10,
      safeSearch = true,
      region = 'us',
      language = 'en',
      timeout = 15000,
      includeSnippets = true,
      customEndpoint,
      apiKey,
    } = parameters;

    try {
      // Validate query
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        throw new ToolValidationError(
          'Query must be a non-empty string',
          'INVALID_QUERY',
          this.name
        );
      }

      // Validate custom endpoint if using custom engine
      if (engine === 'custom' && !customEndpoint) {
        throw new ToolValidationError(
          'Custom endpoint required when using custom engine',
          'MISSING_ENDPOINT',
          this.name
        );
      }

      let results: SearchResult[] = [];

      // Execute search based on engine
      switch (engine) {
        case 'duckduckgo':
          results = await this.searchDuckDuckGo(query, maxResults, safeSearch, region, timeout);
          break;
        case 'searx':
          results = await this.searchSearx(query, maxResults, safeSearch, language, timeout);
          break;
        case 'custom':
          results = await this.searchCustom(customEndpoint!, query, maxResults, apiKey, timeout);
          break;
        default:
          throw new ToolValidationError(
            `Unsupported search engine: ${engine}`,
            'UNSUPPORTED_ENGINE',
            this.name
          );
      }

      // Filter and format results
      const filteredResults = results
        .slice(0, maxResults)
        .map(result => ({
          title: result.title,
          url: result.url,
          snippet: includeSnippets ? result.snippet : undefined,
          displayUrl: result.displayUrl,
        }))
        .filter(result => result.title && result.url);

      return {
        success: true,
        result: {
          query,
          results: filteredResults,
          count: filteredResults.length,
          engine,
          metadata: {
            safeSearch,
            region,
            language,
            maxResults,
          },
        },
        metadata: {
          operation: 'web_search',
          query,
          engine,
          resultCount: filteredResults.length,
        },
      };

    } catch (error) {
      return {
        success: false,
        error: `Web search failed: ${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          operation: 'web_search',
          query,
          engine,
        },
      };
    }
  }

  private async searchDuckDuckGo(
    query: string,
    maxResults: number,
    safeSearch: boolean,
    region: string,
    timeout: number
  ): Promise<SearchResult[]> {
    const encodedQuery = encodeURIComponent(query);
    const safeSearchParam = safeSearch ? '1' : '-1';
    const url = `https://api.duckduckgo.com/?q=${encodedQuery}&format=json&no_html=1&skip_disambig=1&safe_search=${safeSearchParam}&region=${region}`;

    const response = await fetch(url, {
      timeout,
      headers: {
        'User-Agent': 'arien-ai-cli/1.0.0',
      },
    });

    if (!response.ok) {
      throw new Error(`DuckDuckGo API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json() as any;
    const results: SearchResult[] = [];

    // Process instant answer
    if (data.Answer) {
      results.push({
        title: 'Instant Answer',
        url: data.AnswerURL || '',
        snippet: data.Answer,
        displayUrl: data.AnswerURL,
      });
    }

    // Process abstract
    if (data.Abstract) {
      results.push({
        title: data.Heading || 'Abstract',
        url: data.AbstractURL || '',
        snippet: data.Abstract,
        displayUrl: data.AbstractURL,
      });
    }

    // Process related topics
    if (data.RelatedTopics) {
      for (const topic of data.RelatedTopics.slice(0, maxResults - results.length)) {
        if (topic.Text && topic.FirstURL) {
          results.push({
            title: topic.Text.split(' - ')[0] || topic.Text,
            url: topic.FirstURL,
            snippet: topic.Text,
            displayUrl: topic.FirstURL,
          });
        }
      }
    }

    return results.slice(0, maxResults);
  }

  private async searchSearx(
    query: string,
    maxResults: number,
    safeSearch: boolean,
    language: string,
    timeout: number
  ): Promise<SearchResult[]> {
    // Use a public SearX instance
    const searxInstance = 'https://searx.be';
    const encodedQuery = encodeURIComponent(query);
    const safeSearchParam = safeSearch ? '1' : '0';
    const url = `${searxInstance}/search?q=${encodedQuery}&format=json&safesearch=${safeSearchParam}&lang=${language}`;

    const response = await fetch(url, {
      timeout,
      headers: {
        'User-Agent': 'arien-ai-cli/1.0.0',
      },
    });

    if (!response.ok) {
      throw new Error(`SearX API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json() as any;
    const results: SearchResult[] = [];

    if (data.results) {
      for (const result of data.results.slice(0, maxResults)) {
        results.push({
          title: result.title || '',
          url: result.url || '',
          snippet: result.content || '',
          displayUrl: result.pretty_url || result.url,
        });
      }
    }

    return results;
  }

  private async searchCustom(
    endpoint: string,
    query: string,
    maxResults: number,
    apiKey?: string,
    timeout?: number
  ): Promise<SearchResult[]> {
    const headers: Record<string, string> = {
      'User-Agent': 'arien-ai-cli/1.0.0',
      'Content-Type': 'application/json',
    };

    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }

    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        query,
        maxResults,
      }),
      timeout,
    });

    if (!response.ok) {
      throw new Error(`Custom search API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json() as any;
    
    // Expect the custom API to return results in a standard format
    if (!data.results || !Array.isArray(data.results)) {
      throw new Error('Invalid response format from custom search API');
    }

    return data.results.map((result: any) => ({
      title: result.title || '',
      url: result.url || '',
      snippet: result.snippet || result.description || '',
      displayUrl: result.displayUrl || result.url,
    }));
  }

  requiresApproval(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): boolean {
    // Web search requires approval based on approval level
    switch (context.approvalLevel) {
      case 'yolo':
        return false;
      case 'auto-edit':
        return false; // Web search is generally safe
      case 'default':
      default:
        return true;
    }
  }

  validateParameters(parameters: Record<string, any>): boolean {
    return !!parameters.query;
  }

  getCategory(): ToolCategory {
    return ToolCategory.WEB;
  }

  isAvailable(): boolean {
    return true;
  }
}
