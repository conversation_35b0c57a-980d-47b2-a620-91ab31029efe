import chalk from 'chalk';

export interface ErrorContext {
  operation?: string;
  component?: string;
  userId?: string;
  sessionId?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface ErrorDetails {
  code: string;
  message: string;
  context?: ErrorContext;
  cause?: Error;
  stack?: string;
  recoverable?: boolean;
  userMessage?: string;
}

export class BaseError extends Error {
  public readonly code: string;
  public readonly context?: ErrorContext;
  public readonly cause?: Error;
  public readonly recoverable: boolean;
  public readonly userMessage?: string;

  constructor(details: ErrorDetails) {
    super(details.message);
    this.name = this.constructor.name;
    this.code = details.code;
    this.context = details.context;
    this.cause = details.cause;
    this.recoverable = details.recoverable ?? false;
    this.userMessage = details.userMessage;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toJSON(): Record<string, any> {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      context: this.context,
      recoverable: this.recoverable,
      userMessage: this.userMessage,
      stack: this.stack,
    };
  }

  toString(): string {
    let result = `${this.name} [${this.code}]: ${this.message}`;
    
    if (this.context?.operation) {
      result += ` (operation: ${this.context.operation})`;
    }
    
    if (this.cause) {
      result += `\nCaused by: ${this.cause.message}`;
    }
    
    return result;
  }
}

// Specific error types
export class ValidationError extends BaseError {
  constructor(message: string, code: string = 'VALIDATION_ERROR', context?: ErrorContext) {
    super({
      code,
      message,
      context,
      recoverable: true,
      userMessage: `Invalid input: ${message}`,
    });
  }
}

export class ConfigurationError extends BaseError {
  constructor(message: string, code: string = 'CONFIG_ERROR', context?: ErrorContext) {
    super({
      code,
      message,
      context,
      recoverable: true,
      userMessage: `Configuration error: ${message}`,
    });
  }
}

export class NetworkError extends BaseError {
  constructor(message: string, code: string = 'NETWORK_ERROR', context?: ErrorContext, cause?: Error) {
    super({
      code,
      message,
      context,
      cause,
      recoverable: true,
      userMessage: 'Network connection failed. Please check your internet connection and try again.',
    });
  }
}

export class AuthenticationError extends BaseError {
  constructor(message: string, code: string = 'AUTH_ERROR', context?: ErrorContext) {
    super({
      code,
      message,
      context,
      recoverable: true,
      userMessage: 'Authentication failed. Please check your credentials and try again.',
    });
  }
}

export class PermissionError extends BaseError {
  constructor(message: string, code: string = 'PERMISSION_ERROR', context?: ErrorContext) {
    super({
      code,
      message,
      context,
      recoverable: false,
      userMessage: 'Permission denied. You do not have access to perform this operation.',
    });
  }
}

export class ResourceNotFoundError extends BaseError {
  constructor(resource: string, code: string = 'NOT_FOUND', context?: ErrorContext) {
    super({
      code,
      message: `Resource not found: ${resource}`,
      context,
      recoverable: false,
      userMessage: `The requested resource "${resource}" was not found.`,
    });
  }
}

export class TimeoutError extends BaseError {
  constructor(operation: string, timeout: number, code: string = 'TIMEOUT', context?: ErrorContext) {
    super({
      code,
      message: `Operation "${operation}" timed out after ${timeout}ms`,
      context,
      recoverable: true,
      userMessage: `The operation timed out. Please try again.`,
    });
  }
}

export class RateLimitError extends BaseError {
  constructor(retryAfter?: number, code: string = 'RATE_LIMIT', context?: ErrorContext) {
    const message = retryAfter 
      ? `Rate limit exceeded. Retry after ${retryAfter} seconds.`
      : 'Rate limit exceeded.';
    
    super({
      code,
      message,
      context,
      recoverable: true,
      userMessage: 'Too many requests. Please wait a moment and try again.',
    });
  }
}

export class InternalError extends BaseError {
  constructor(message: string, code: string = 'INTERNAL_ERROR', context?: ErrorContext, cause?: Error) {
    super({
      code,
      message,
      context,
      cause,
      recoverable: false,
      userMessage: 'An internal error occurred. Please try again or contact support.',
    });
  }
}

// Error handler class
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorListeners: ((error: BaseError) => void)[] = [];

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  addErrorListener(listener: (error: BaseError) => void): void {
    this.errorListeners.push(listener);
  }

  removeErrorListener(listener: (error: BaseError) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  handle(error: Error | BaseError, context?: ErrorContext): BaseError {
    let handledError: BaseError;

    if (error instanceof BaseError) {
      handledError = error;
    } else {
      // Convert regular Error to BaseError
      handledError = this.convertError(error, context);
    }

    // Notify listeners
    this.errorListeners.forEach(listener => {
      try {
        listener(handledError);
      } catch (listenerError) {
        console.error('Error in error listener:', listenerError);
      }
    });

    return handledError;
  }

  private convertError(error: Error, context?: ErrorContext): BaseError {
    const message = error.message || 'Unknown error';
    
    // Try to categorize the error based on common patterns
    if (message.includes('ENOTFOUND') || message.includes('ECONNREFUSED')) {
      return new NetworkError(message, 'NETWORK_CONNECTION_ERROR', context, error);
    }
    
    if (message.includes('timeout') || message.includes('ETIMEDOUT')) {
      return new TimeoutError(context?.operation || 'unknown', 0, 'OPERATION_TIMEOUT', context);
    }
    
    if (message.includes('permission') || message.includes('EACCES')) {
      return new PermissionError(message, 'FILE_PERMISSION_ERROR', context);
    }
    
    if (message.includes('not found') || message.includes('ENOENT')) {
      return new ResourceNotFoundError(message, 'RESOURCE_NOT_FOUND', context);
    }
    
    if (message.includes('authentication') || message.includes('unauthorized')) {
      return new AuthenticationError(message, 'AUTH_FAILED', context);
    }

    // Default to internal error
    return new InternalError(message, 'UNKNOWN_ERROR', context, error);
  }

  formatError(error: BaseError, includeStack: boolean = false): string {
    let formatted = chalk.red(`Error [${error.code}]: ${error.userMessage || error.message}`);
    
    if (error.context?.operation) {
      formatted += chalk.gray(` (${error.context.operation})`);
    }
    
    if (includeStack && error.stack) {
      formatted += '\n' + chalk.gray(error.stack);
    }
    
    return formatted;
  }

  isRecoverable(error: Error | BaseError): boolean {
    if (error instanceof BaseError) {
      return error.recoverable;
    }
    
    // For regular errors, assume they might be recoverable
    return true;
  }

  shouldRetry(error: Error | BaseError): boolean {
    if (error instanceof BaseError) {
      return error.recoverable && [
        'NETWORK_ERROR',
        'TIMEOUT',
        'RATE_LIMIT',
        'NETWORK_CONNECTION_ERROR',
        'OPERATION_TIMEOUT',
      ].includes(error.code);
    }
    
    const message = error.message?.toLowerCase() || '';
    return (
      message.includes('timeout') ||
      message.includes('network') ||
      message.includes('connection') ||
      message.includes('econnreset') ||
      message.includes('econnrefused')
    );
  }
}

// Utility functions
export function createErrorContext(
  operation?: string,
  component?: string,
  metadata?: Record<string, any>
): ErrorContext {
  return {
    operation,
    component,
    timestamp: new Date(),
    metadata,
  };
}

export function wrapAsync<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: ErrorContext
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      const handler = ErrorHandler.getInstance();
      throw handler.handle(error as Error, context);
    }
  };
}

export function wrapSync<T extends any[], R>(
  fn: (...args: T) => R,
  context?: ErrorContext
): (...args: T) => R {
  return (...args: T): R => {
    try {
      return fn(...args);
    } catch (error) {
      const handler = ErrorHandler.getInstance();
      throw handler.handle(error as Error, context);
    }
  };
}

// Global error handler setup
export function setupGlobalErrorHandling(): void {
  const handler = ErrorHandler.getInstance();

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    const handledError = handler.handle(error, {
      operation: 'unhandled_promise_rejection',
      component: 'global',
    });
    
    console.error(chalk.red('Unhandled Promise Rejection:'));
    console.error(handler.formatError(handledError, true));
    
    // Exit gracefully
    process.exit(1);
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    const handledError = handler.handle(error, {
      operation: 'uncaught_exception',
      component: 'global',
    });
    
    console.error(chalk.red('Uncaught Exception:'));
    console.error(handler.formatError(handledError, true));
    
    // Exit gracefully
    process.exit(1);
  });
}
