import chalk from 'chalk';
import stripAnsi from 'strip-ansi';

export interface TableColumn {
  header: string;
  key: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  formatter?: (value: any) => string;
}

export interface TableOptions {
  border?: boolean;
  padding?: number;
  maxWidth?: number;
  truncate?: boolean;
  colors?: {
    header?: string;
    border?: string;
    even?: string;
    odd?: string;
  };
}

export class TableFormatter {
  private options: Required<TableOptions>;

  constructor(options: TableOptions = {}) {
    this.options = {
      border: true,
      padding: 1,
      maxWidth: process.stdout.columns || 80,
      truncate: true,
      colors: {
        header: 'cyan',
        border: 'gray',
        even: 'white',
        odd: 'gray',
      },
      ...options,
    };
  }

  format(data: any[], columns: TableColumn[]): string {
    if (data.length === 0) {
      return chalk.gray('No data to display');
    }

    // Calculate column widths
    const widths = this.calculateColumnWidths(data, columns);
    
    // Build table
    const lines: string[] = [];
    
    if (this.options.border) {
      lines.push(this.createBorderLine(widths));
    }
    
    // Header
    lines.push(this.createHeaderRow(columns, widths));
    
    if (this.options.border) {
      lines.push(this.createBorderLine(widths));
    }
    
    // Data rows
    data.forEach((row, index) => {
      const isEven = index % 2 === 0;
      lines.push(this.createDataRow(row, columns, widths, isEven));
    });
    
    if (this.options.border) {
      lines.push(this.createBorderLine(widths));
    }
    
    return lines.join('\n');
  }

  private calculateColumnWidths(data: any[], columns: TableColumn[]): number[] {
    const widths = columns.map(col => col.width || col.header.length);
    
    // Calculate based on data if no fixed width
    columns.forEach((col, index) => {
      if (!col.width) {
        const maxDataWidth = Math.max(
          ...data.map(row => {
            const value = col.formatter ? col.formatter(row[col.key]) : String(row[col.key] || '');
            return stripAnsi(value).length;
          })
        );
        widths[index] = Math.max(widths[index], maxDataWidth);
      }
    });
    
    // Adjust for max width if needed
    const totalWidth = widths.reduce((sum, w) => sum + w, 0) + 
                      (columns.length - 1) * 3 + // separators
                      (this.options.border ? 4 : 0); // borders
    
    if (totalWidth > this.options.maxWidth && this.options.truncate) {
      const excess = totalWidth - this.options.maxWidth;
      const flexColumns = columns.map((col, i) => ({ index: i, flexible: !col.width }))
                                 .filter(c => c.flexible);
      
      if (flexColumns.length > 0) {
        const reductionPerColumn = Math.ceil(excess / flexColumns.length);
        flexColumns.forEach(({ index }) => {
          widths[index] = Math.max(3, widths[index] - reductionPerColumn);
        });
      }
    }
    
    return widths;
  }

  private createBorderLine(widths: number[]): string {
    const parts = widths.map(width => '─'.repeat(width + this.options.padding * 2));
    const line = '┌' + parts.join('┬') + '┐';
    return chalk[this.options.colors.border as any](line);
  }

  private createHeaderRow(columns: TableColumn[], widths: number[]): string {
    const cells = columns.map((col, index) => {
      const content = this.alignText(col.header, widths[index], col.align || 'left');
      return chalk[this.options.colors.header as any](content);
    });
    
    const padding = ' '.repeat(this.options.padding);
    const separator = chalk[this.options.colors.border as any]('│');
    
    if (this.options.border) {
      return separator + padding + cells.join(padding + separator + padding) + padding + separator;
    } else {
      return padding + cells.join(padding + ' ' + padding) + padding;
    }
  }

  private createDataRow(row: any, columns: TableColumn[], widths: number[], isEven: boolean): string {
    const colorKey = isEven ? 'even' : 'odd';
    const cells = columns.map((col, index) => {
      let value = row[col.key];
      if (col.formatter) {
        value = col.formatter(value);
      } else {
        value = String(value || '');
      }
      
      const content = this.alignText(value, widths[index], col.align || 'left');
      return chalk[this.options.colors[colorKey] as any](content);
    });
    
    const padding = ' '.repeat(this.options.padding);
    const separator = chalk[this.options.colors.border as any]('│');
    
    if (this.options.border) {
      return separator + padding + cells.join(padding + separator + padding) + padding + separator;
    } else {
      return padding + cells.join(padding + ' ' + padding) + padding;
    }
  }

  private alignText(text: string, width: number, align: 'left' | 'center' | 'right'): string {
    const cleanText = stripAnsi(text);
    const ansiLength = text.length - cleanText.length;
    
    if (cleanText.length > width) {
      const truncated = cleanText.substring(0, width - 3) + '...';
      return text.substring(0, truncated.length + ansiLength);
    }
    
    const padding = width - cleanText.length;
    
    switch (align) {
      case 'center':
        const leftPad = Math.floor(padding / 2);
        const rightPad = padding - leftPad;
        return ' '.repeat(leftPad) + text + ' '.repeat(rightPad);
      case 'right':
        return ' '.repeat(padding) + text;
      case 'left':
      default:
        return text + ' '.repeat(padding);
    }
  }
}

// Progress bar utilities
export class ProgressBar {
  private total: number;
  private current: number = 0;
  private width: number;
  private label: string;

  constructor(total: number, width: number = 40, label: string = 'Progress') {
    this.total = total;
    this.width = width;
    this.label = label;
  }

  update(current: number, message?: string): void {
    this.current = current;
    this.render(message);
  }

  increment(message?: string): void {
    this.current++;
    this.render(message);
  }

  complete(message?: string): void {
    this.current = this.total;
    this.render(message);
    console.log(); // New line after completion
  }

  private render(message?: string): void {
    const percentage = Math.min(100, Math.max(0, (this.current / this.total) * 100));
    const filled = Math.round((this.width * percentage) / 100);
    const empty = this.width - filled;
    
    const bar = chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
    const percent = `${percentage.toFixed(1)}%`.padStart(6);
    const counter = `${this.current}/${this.total}`.padStart(10);
    
    let line = `${this.label}: [${bar}] ${percent} ${counter}`;
    
    if (message) {
      line += ` ${message}`;
    }
    
    // Clear line and write new content
    process.stdout.write('\r' + ' '.repeat(process.stdout.columns || 80) + '\r');
    process.stdout.write(line);
  }
}

// Spinner utilities
export class Spinner {
  private frames: string[] = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
  private interval?: NodeJS.Timeout;
  private currentFrame: number = 0;
  private message: string;

  constructor(message: string = 'Loading...') {
    this.message = message;
  }

  start(): void {
    this.interval = setInterval(() => {
      const frame = this.frames[this.currentFrame];
      process.stdout.write(`\r${chalk.cyan(frame)} ${this.message}`);
      this.currentFrame = (this.currentFrame + 1) % this.frames.length;
    }, 80);
  }

  stop(finalMessage?: string): void {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = undefined;
    }
    
    // Clear the line
    process.stdout.write('\r' + ' '.repeat(process.stdout.columns || 80) + '\r');
    
    if (finalMessage) {
      console.log(finalMessage);
    }
  }

  updateMessage(message: string): void {
    this.message = message;
  }
}

// Text formatting utilities
export const formatters = {
  // File size formatting
  fileSize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
  },

  // Duration formatting
  duration: (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  },

  // Date formatting
  date: (date: Date | string): string => {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
  },

  // Relative time formatting
  relativeTime: (date: Date | string): string => {
    const d = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diff = now.getTime() - d.getTime();
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days} day${days !== 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    return `${seconds} second${seconds !== 1 ? 's' : ''} ago`;
  },

  // Number formatting
  number: (num: number): string => {
    return num.toLocaleString();
  },

  // Percentage formatting
  percentage: (value: number, total: number): string => {
    const percent = (value / total) * 100;
    return `${percent.toFixed(1)}%`;
  },

  // Truncate text
  truncate: (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  },

  // Wrap text
  wrap: (text: string, width: number): string[] => {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';
    
    for (const word of words) {
      if ((currentLine + word).length > width) {
        if (currentLine) {
          lines.push(currentLine.trim());
          currentLine = word + ' ';
        } else {
          // Word is longer than width, force break
          lines.push(word);
        }
      } else {
        currentLine += word + ' ';
      }
    }
    
    if (currentLine) {
      lines.push(currentLine.trim());
    }
    
    return lines;
  },
};

// Color utilities
export const colors = {
  success: chalk.green,
  error: chalk.red,
  warning: chalk.yellow,
  info: chalk.blue,
  debug: chalk.gray,
  highlight: chalk.cyan,
  muted: chalk.gray,
  bold: chalk.bold,
  dim: chalk.dim,
};
