import { setTimeout } from 'timers/promises';

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  jitter: boolean;
  retryCondition?: (error: any) => boolean;
  onRetry?: (error: any, attempt: number) => void;
}

export interface CircuitBreakerOptions {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
  expectedErrors?: (error: any) => boolean;
}

export class RetryPolicy {
  private options: RetryOptions;

  constructor(options: Partial<RetryOptions> = {}) {
    this.options = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
      jitter: true,
      ...options,
    };
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Check if we should retry this error
        if (this.options.retryCondition && !this.options.retryCondition(error)) {
          throw error;
        }

        // Don't delay on the last attempt
        if (attempt === this.options.maxAttempts) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = this.calculateDelay(attempt);
        
        // Call retry callback if provided
        if (this.options.onRetry) {
          this.options.onRetry(error, attempt);
        }

        await setTimeout(delay);
      }
    }

    throw lastError;
  }

  private calculateDelay(attempt: number): number {
    let delay = this.options.baseDelay * Math.pow(this.options.backoffFactor, attempt - 1);
    delay = Math.min(delay, this.options.maxDelay);

    if (this.options.jitter) {
      // Add random jitter to prevent thundering herd
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    return Math.floor(delay);
  }
}

export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half-open',
}

export class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private successCount: number = 0;
  private options: CircuitBreakerOptions;

  constructor(options: Partial<CircuitBreakerOptions> = {}) {
    this.options = {
      failureThreshold: 5,
      resetTimeout: 60000,
      monitoringPeriod: 10000,
      ...options,
    };
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.successCount = 0;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) { // Require 3 successes to close
        this.state = CircuitBreakerState.CLOSED;
      }
    }
  }

  private onFailure(error: any): void {
    // Check if this is an expected error that shouldn't count
    if (this.options.expectedErrors && this.options.expectedErrors(error)) {
      return;
    }

    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.options.failureThreshold) {
      this.state = CircuitBreakerState.OPEN;
    }
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.options.resetTimeout;
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  getFailureCount(): number {
    return this.failureCount;
  }

  reset(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = 0;
  }
}

export class RetryableError extends Error {
  constructor(message: string, public retryable: boolean = true) {
    super(message);
    this.name = 'RetryableError';
  }
}

export class NonRetryableError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NonRetryableError';
  }
}

// Common retry conditions
export const retryConditions = {
  // Retry on network errors
  networkErrors: (error: any): boolean => {
    if (error instanceof NonRetryableError) return false;
    if (error instanceof RetryableError) return error.retryable;
    
    const message = error.message?.toLowerCase() || '';
    return (
      message.includes('timeout') ||
      message.includes('econnreset') ||
      message.includes('econnrefused') ||
      message.includes('enotfound') ||
      message.includes('network') ||
      error.code === 'ETIMEDOUT' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'ENOTFOUND'
    );
  },

  // Retry on HTTP 5xx errors
  httpServerErrors: (error: any): boolean => {
    if (error instanceof NonRetryableError) return false;
    if (error instanceof RetryableError) return error.retryable;
    
    const status = error.status || error.statusCode;
    return status >= 500 && status < 600;
  },

  // Retry on rate limiting
  rateLimitErrors: (error: any): boolean => {
    if (error instanceof NonRetryableError) return false;
    if (error instanceof RetryableError) return error.retryable;
    
    const status = error.status || error.statusCode;
    return status === 429 || status === 503;
  },

  // Combine multiple conditions
  combine: (...conditions: ((error: any) => boolean)[]): ((error: any) => boolean) => {
    return (error: any) => conditions.some(condition => condition(error));
  },
};

// Predefined retry policies
export const retryPolicies = {
  // Quick retry for fast operations
  quick: new RetryPolicy({
    maxAttempts: 3,
    baseDelay: 100,
    maxDelay: 1000,
    backoffFactor: 2,
    jitter: true,
    retryCondition: retryConditions.networkErrors,
  }),

  // Standard retry for most operations
  standard: new RetryPolicy({
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    jitter: true,
    retryCondition: retryConditions.combine(
      retryConditions.networkErrors,
      retryConditions.httpServerErrors
    ),
  }),

  // Aggressive retry for critical operations
  aggressive: new RetryPolicy({
    maxAttempts: 5,
    baseDelay: 500,
    maxDelay: 30000,
    backoffFactor: 2,
    jitter: true,
    retryCondition: retryConditions.combine(
      retryConditions.networkErrors,
      retryConditions.httpServerErrors,
      retryConditions.rateLimitErrors
    ),
  }),

  // Conservative retry for expensive operations
  conservative: new RetryPolicy({
    maxAttempts: 2,
    baseDelay: 2000,
    maxDelay: 10000,
    backoffFactor: 3,
    jitter: true,
    retryCondition: retryConditions.networkErrors,
  }),
};

// Predefined circuit breakers
export const circuitBreakers = {
  // Standard circuit breaker
  standard: new CircuitBreaker({
    failureThreshold: 5,
    resetTimeout: 60000,
    monitoringPeriod: 10000,
  }),

  // Sensitive circuit breaker for critical services
  sensitive: new CircuitBreaker({
    failureThreshold: 3,
    resetTimeout: 30000,
    monitoringPeriod: 5000,
  }),

  // Tolerant circuit breaker for less critical services
  tolerant: new CircuitBreaker({
    failureThreshold: 10,
    resetTimeout: 120000,
    monitoringPeriod: 20000,
  }),
};
