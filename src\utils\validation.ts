import { z } from 'zod';
import { ValidationError } from './errors.js';

export interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: ValidationError[];
}

export class Validator {
  static string(options: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    required?: boolean;
    trim?: boolean;
  } = {}): z.ZodString {
    let schema = z.string();
    
    if (options.required !== false) {
      schema = schema.min(1, 'This field is required');
    }
    
    if (options.minLength !== undefined) {
      schema = schema.min(options.minLength, `Must be at least ${options.minLength} characters`);
    }
    
    if (options.maxLength !== undefined) {
      schema = schema.max(options.maxLength, `Must be no more than ${options.maxLength} characters`);
    }
    
    if (options.pattern) {
      schema = schema.regex(options.pattern, 'Invalid format');
    }
    
    if (options.trim) {
      schema = schema.transform(val => val.trim());
    }
    
    return schema;
  }

  static number(options: {
    min?: number;
    max?: number;
    integer?: boolean;
    positive?: boolean;
  } = {}): z.ZodNumber {
    let schema = z.number();
    
    if (options.min !== undefined) {
      schema = schema.min(options.min, `Must be at least ${options.min}`);
    }
    
    if (options.max !== undefined) {
      schema = schema.max(options.max, `Must be no more than ${options.max}`);
    }
    
    if (options.integer) {
      schema = schema.int('Must be an integer');
    }
    
    if (options.positive) {
      schema = schema.positive('Must be positive');
    }
    
    return schema;
  }

  static email(): z.ZodString {
    return z.string().email('Invalid email address');
  }

  static url(): z.ZodString {
    return z.string().url('Invalid URL');
  }

  static path(): z.ZodString {
    return z.string().refine(
      (path) => {
        // Basic path validation - no null bytes, reasonable length
        return !path.includes('\0') && path.length < 4096;
      },
      'Invalid file path'
    );
  }

  static apiKey(): z.ZodString {
    return z.string()
      .min(10, 'API key too short')
      .max(200, 'API key too long')
      .regex(/^[a-zA-Z0-9_-]+$/, 'API key contains invalid characters');
  }

  static validate<T>(schema: z.ZodSchema<T>, data: unknown): ValidationResult<T> {
    try {
      const result = schema.parse(data);
      return { success: true, data: result };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => 
          new ValidationError(
            `${err.path.join('.')}: ${err.message}`,
            'VALIDATION_ERROR'
          )
        );
        return { success: false, errors };
      }
      
      return {
        success: false,
        errors: [new ValidationError('Validation failed', 'VALIDATION_ERROR')]
      };
    }
  }

  static async validateAsync<T>(
    schema: z.ZodSchema<T>,
    data: unknown
  ): Promise<ValidationResult<T>> {
    try {
      const result = await schema.parseAsync(data);
      return { success: true, data: result };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => 
          new ValidationError(
            `${err.path.join('.')}: ${err.message}`,
            'VALIDATION_ERROR'
          )
        );
        return { success: false, errors };
      }
      
      return {
        success: false,
        errors: [new ValidationError('Validation failed', 'VALIDATION_ERROR')]
      };
    }
  }
}

// Common validation schemas
export const schemas = {
  // File operations
  filePath: z.string()
    .min(1, 'File path is required')
    .max(4096, 'File path too long')
    .refine(path => !path.includes('\0'), 'Invalid file path'),

  fileName: z.string()
    .min(1, 'File name is required')
    .max(255, 'File name too long')
    .refine(name => !/[<>:"/\\|?*]/.test(name), 'Invalid file name characters'),

  // Network
  url: z.string().url('Invalid URL'),
  
  port: z.number()
    .int('Port must be an integer')
    .min(1, 'Port must be at least 1')
    .max(65535, 'Port must be no more than 65535'),

  // API
  apiKey: z.string()
    .min(10, 'API key too short')
    .max(200, 'API key too long'),

  // User input
  nonEmptyString: z.string().min(1, 'This field is required'),
  
  optionalString: z.string().optional(),

  // Configuration
  approvalLevel: z.enum(['default', 'auto-edit', 'yolo']),
  
  provider: z.enum(['openai', 'anthropic', 'deepseek', 'google']),

  // Tool parameters
  toolName: z.string()
    .min(1, 'Tool name is required')
    .regex(/^[a-zA-Z][a-zA-Z0-9_-]*$/, 'Invalid tool name format'),

  // Search and filtering
  searchQuery: z.string()
    .min(1, 'Search query is required')
    .max(1000, 'Search query too long'),

  limit: z.number()
    .int('Limit must be an integer')
    .min(1, 'Limit must be at least 1')
    .max(1000, 'Limit must be no more than 1000'),

  // Time and dates
  timeout: z.number()
    .int('Timeout must be an integer')
    .min(100, 'Timeout must be at least 100ms')
    .max(300000, 'Timeout must be no more than 5 minutes'),

  // File content
  encoding: z.enum(['utf8', 'ascii', 'base64', 'hex', 'latin1']),

  // Glob patterns
  globPattern: z.string()
    .min(1, 'Glob pattern is required')
    .max(500, 'Glob pattern too long'),

  // Regular expressions
  regexPattern: z.string()
    .min(1, 'Regex pattern is required')
    .refine(pattern => {
      try {
        new RegExp(pattern);
        return true;
      } catch {
        return false;
      }
    }, 'Invalid regular expression'),
};

// Validation helpers
export const validators = {
  // Check if a string is a valid JSON
  isValidJson: (str: string): boolean => {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  },

  // Check if a string is a valid regex
  isValidRegex: (pattern: string): boolean => {
    try {
      new RegExp(pattern);
      return true;
    } catch {
      return false;
    }
  },

  // Check if a path is safe (no directory traversal)
  isSafePath: (path: string, basePath: string): boolean => {
    const { resolve, relative } = require('path');
    const resolvedPath = resolve(basePath, path);
    const relativePath = relative(basePath, resolvedPath);
    return !relativePath.startsWith('..') && !relativePath.includes('..');
  },

  // Check if a URL is safe (allowed protocols)
  isSafeUrl: (url: string, allowedProtocols: string[] = ['http:', 'https:']): boolean => {
    try {
      const parsed = new URL(url);
      return allowedProtocols.includes(parsed.protocol);
    } catch {
      return false;
    }
  },

  // Check if a file extension is allowed
  isAllowedExtension: (filename: string, allowedExtensions: string[]): boolean => {
    const ext = filename.toLowerCase().split('.').pop();
    return ext ? allowedExtensions.includes(`.${ext}`) : false;
  },

  // Check if a string contains only safe characters
  isSafeString: (str: string): boolean => {
    // No null bytes, control characters (except common whitespace)
    return !/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(str);
  },

  // Validate command line arguments
  isSafeCommand: (command: string, allowedCommands: string[] = []): boolean => {
    if (allowedCommands.length > 0) {
      const baseCommand = command.split(' ')[0];
      return allowedCommands.includes(baseCommand);
    }
    
    // Basic safety checks
    const dangerousPatterns = [
      /[;&|`$(){}[\]]/,  // Shell metacharacters
      /\.\./,            // Directory traversal
      /^sudo\s/,         // Privilege escalation
      /^su\s/,           // User switching
      /rm\s+-rf/,        // Dangerous deletion
    ];
    
    return !dangerousPatterns.some(pattern => pattern.test(command));
  },

  // Validate environment variable names
  isValidEnvVar: (name: string): boolean => {
    return /^[A-Z][A-Z0-9_]*$/.test(name);
  },

  // Validate semantic version
  isValidSemVer: (version: string): boolean => {
    const semverRegex = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
    return semverRegex.test(version);
  },
};

// Sanitization helpers
export const sanitizers = {
  // Sanitize filename
  filename: (name: string): string => {
    return name
      .replace(/[<>:"/\\|?*]/g, '_')  // Replace invalid characters
      .replace(/\s+/g, '_')           // Replace spaces with underscores
      .replace(/_{2,}/g, '_')         // Collapse multiple underscores
      .replace(/^_+|_+$/g, '')        // Remove leading/trailing underscores
      .substring(0, 255);             // Limit length
  },

  // Sanitize path
  path: (path: string): string => {
    return path
      .replace(/\\/g, '/')            // Normalize separators
      .replace(/\/+/g, '/')           // Collapse multiple slashes
      .replace(/\/\./g, '/')          // Remove current directory references
      .replace(/^\/+|\/+$/g, '');     // Remove leading/trailing slashes
  },

  // Sanitize command
  command: (cmd: string): string => {
    return cmd
      .replace(/[;&|`$(){}[\]]/g, '')  // Remove shell metacharacters
      .replace(/\s+/g, ' ')            // Normalize whitespace
      .trim();
  },

  // Sanitize HTML (basic)
  html: (html: string): string => {
    return html
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  },

  // Sanitize for shell
  shell: (str: string): string => {
    return str.replace(/[^a-zA-Z0-9._-]/g, '');
  },
};
